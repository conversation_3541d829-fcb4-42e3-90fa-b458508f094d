{"$schema": "https://biomejs.dev/schemas/2.0.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "includes": ["src/**/*.{js,jsx,ts,tsx}", "*.{js,jsx,ts,tsx}"]}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 80, "attributePosition": "auto"}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"recommended": true}, "complexity": {"recommended": true, "noExcessiveCognitiveComplexity": "error", "noVoid": "off"}, "correctness": {"recommended": true, "noUndeclaredVariables": "error", "noUnusedVariables": "error"}, "nursery": {"recommended": false}, "performance": {"recommended": true}, "security": {"recommended": true}, "style": {"recommended": true, "noNonNullAssertion": "off", "useImportType": "error"}, "suspicious": {"recommended": true, "noExplicitAny": "warn", "noArrayIndexKey": "off"}}}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false, "quoteStyle": "double", "attributePosition": "auto"}}, "json": {"formatter": {"enabled": true}}}