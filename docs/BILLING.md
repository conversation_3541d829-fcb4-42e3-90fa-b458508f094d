---
title: Clerk billing for B2C SaaS
description: Clerk billing is a feature that allows you to create and manage
  plans and features for your application.
lastUpdated: 2025-06-24T16:19:54.000Z
---

Clerk billing for B2C SaaS allows you to create plans and manage subscriptions **for individual users** in your application. If you'd like to charge companies or organizations, see [Billing for B2B SaaS](/docs/billing/b2b-saas). You can also combine both B2C and B2B billing in the same application.

## Enable billing

To enable billing for your application, navigate to the [**Billing Settings**](https://dashboard.clerk.com/last-active?path=billing/settings) page in the Clerk Dashboard. This page will guide you through enabling billing for your application.

Clerk billing costs just 0.7% per transaction, plus <PERSON><PERSON>'s transaction fees which are paid directly to Strip<PERSON>.

### Payment gateway

Once you have enabled billing, you will see the following **Payment gateway** options for collecting payments via Stripe:

* **Clerk development gateway**: A shared **test** Stripe account so developers can get started testing and building with billing **in development** without needing to create and configure a Stripe account.
* **Stripe account**: Use your own Stripe account.

## Create a plan

Subscription plans are what your users subscribe to. There is no limit to the number of plans you can create.

To create a plan, navigate to the [**Plans**](https://dashboard.clerk.com/last-active?path=billing/plans) page in the Clerk Dashboard. Here, you can create, edit, and delete plans. To setup B2C billing, select the **Plans for Users** tab and select **Add Plan**. When creating a plan, you can also create features for the plan; see the next section for more information.

> \[!TIP]
> What is the **Publicly available** option?
>
> ***
>
> Plans appear in some Clerk components depending on what kind of plan it is. All plans can appear in the `<PricingTable />` component. If it's a user plan, it can appear in the `<UserProfile />` component. When creating or editing a plan, if you'd like to hide it from appearing in Clerk components, you can toggle the **Publicly available** option off.

## Add features to a plan

Features make it easy to give entitlements to your plans. You can add any number of features to a plan.

You can add a feature to a plan when you are creating a plan. To add it after a plan is created:

1. Navigate to the [**Plans**](https://dashboard.clerk.com/last-active?path=billing/plans) page in the Clerk Dashboard.
2. Select the plan you'd like to add a feature to.
3. In the **Features** section, select **Add Feature**.

> \[!TIP]
> What is the **Publicly available** option?
>
> ***
>
> Plans appear in some Clerk components depending on what kind of plan it is. All plans can appear in the `<PricingTable />` component. If it's a user plan, it can appear in the `<UserProfile />` component. When adding a feature to a plan, it will also automatically appear in the corresponding plan. When creating or editing a feature, if you'd like to hide it from appearing in Clerk components, you can toggle the **Publicly available** option off.

## Create a pricing page

You can create a pricing page by using the [`<PricingTable />`](/docs/components/pricing-table) component. This component displays a table of plans and features that users can subscribe to. **It's recommended to create a dedicated page**, as shown in the following example.

```tsx {{ filename: 'app/pricing/page.tsx' }}
import { PricingTable } from '@clerk/nextjs'

export default function Page() {
  return (
    <div style={{ maxWidth: '800px', margin: '0 auto', padding: '0 1rem' }}>
      <PricingTable />
    </div>
  )
}
```

## Control access with features and plans

You can use Clerk's features and plans to gate access to the content. There are a few ways to do this, but the recommended and simplest way is either using the [`has()`](/docs/references/backend/types/auth-object#has) method or the [`<Protect>`](/docs/components/protect) component.

The `has()` method is available for any JavaScript framework, while `<Protect>` is only available for React-based frameworks.

### Example: Using `has()`

Use the `has()` method to test if the user has access to a **plan**:

```jsx
const { has } = await auth()
const hasPremiumAccess = has({ plan: 'gold' })
```

Or a **feature**:

```jsx
const { has } = await auth()
const hasPremiumAccess = has({ feature: 'widgets' })
```

The [`has()`](/docs/references/backend/types/auth-object#has) method checks if the user has been granted a specific type of access control (role, permission, feature, or plan) and returns a boolean value. It is available on the [`auth` object](/docs/references/backend/types/auth-object) on the server. Depending on the framework you are using, you will access the `auth` object differently.

The following example accesses the `auth` object and the `has()` method using the [Next.js-specific `auth()` helper](/docs/references/nextjs/auth).

<Tabs items={[ "Plan", "Feature"]}>
  <Tab>
    The following example demonstrates how to use `has()` to check if a user has a plan.

    ```tsx {{ filename: 'app/page.tsx' }}
    import { auth } from '@clerk/nextjs/server'

    export default async function Page() {
      // Use `auth()` helper to access the `has()` method
      const { has } = await auth()

      // Use `has()` method to check if user has a Plan
      const hasBronzePlan = has({ plan: 'bronze' })

      if (!hasBronzePlan) return <h1>Only subscribers to the Bronze plan can access this content.</h1>

      return <h1>For Bronze subscribers only</h1>
    }
    ```
  </Tab>

  <Tab>
    The following example demonstrates how to use `has()` to check if a user has a feature.

    ```tsx {{ filename: 'app/page.tsx' }}
    import { auth } from '@clerk/nextjs/server'

    export default async function Page() {
      // Use `auth()` helper to access the `has()` method
      const { has } = await auth()

      // Use `has()` method to check if user has a Feature
      const hasPremiumAccess = has({ feature: 'premium_access' })

      if (!hasPremiumAccess)
        return <h1>Only subscribers with the Premium Access feature can access this content.</h1>

      return <h1>Our Exclusive Content</h1>
    }
    ```
  </Tab>
</Tabs>

### Example: Using `<Protect>`

The [`<Protect>`](/docs/components/protect) component protects content or even entire routes by checking if the user has been granted a specific type of access control (role, permission, feature, or plan). You can pass a `fallback` prop to `<Protect>` that will be rendered if the user does not have the access control.

<Tabs items={["Plan", "Feature"]}>
  <Tab>
    The following example demonstrates how to use `<Protect>` to protect a page by checking if the user has a plan.

    ```tsx
    export default function ProtectPage() {
      return (
        <Protect
          plan="bronze"
          fallback={<p>Only subscribers to the Bronze plan can access this content.</p>}
        >
          {children}
        </Protect>
      )
    }
    ```
  </Tab>

  <Tab>
    The following example demonstrates how to use `<Protect>` to protect a page by checking if the user has a feature.

    ```tsx
    export default function ProtectPage() {
      return (
        <Protect
          feature="premium_access"
          fallback={<p>Only subscribers with the Premium Access feature can access this content.</p>}
        >
          {children}
        </Protect>
      )
    }
    ```
  </Tab>
</Tabs>

---
title: Request authentication
description: Learn about various ways to make authenticated requests to the
  backend when using Clerk SDKs.
lastUpdated: 2025-06-24T16:19:54.000Z
---

A request is considered “authenticated” when the backend can securely identify the user and device that is making the request. Reasons for making authenticated requests to the backend include:

* Associating the user with the action being performed
* Ensuring the user has permission to make the request
* Keeping an audit log of which device the user is performing actions from

To authenticate requests when using a Clerk SDK, you must pass Clerk's short-lived [session token](/docs/backend-requests/resources/session-tokens) to your server. The session token contains cryptographically signed claims about the user's identity and authentication state. [Read more about making requests](/docs/backend-requests/making-requests).

## Required headers

The following headers are required for Clerk to authenticate a request. It contains information that Clerk uses to determine whether a request is in a signed in or signed out state, or if a [handshake](/docs/how-clerk-works/overview#the-handshake) must be performed.

* [`Authorization`](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Authorization): This should include the user's session token as a Bearer token.
* [`Accept`](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept)
* [`Host`](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Host)
* [`Origin`](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Origin)
* [`Referer`](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Referer)
* [`Sec-Fetch-Dest`](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-Fetch-Dest)
* [`User-Agent`](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/User-Agent)
* [`X-Forwarded-Host`](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Forwarded-Host)
* [`X-Forwarded-Proto`](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Forwarded-Proto)
  * Alternatively, you can use [`CloudFront-Forwarded-Proto`](https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/adding-cloudfront-headers.html#cloudfront-headers-other)

---
title: Customize your session token
description: Learn how to customize the session token that is generated for you by Clerk.
lastUpdated: 2025-06-24T16:19:54.000Z
---

Session tokens are JWTs generated by Clerk on behalf of your instance, and convey an authenticated user session to your backend.

By default, session tokens contain claims that are required for Clerk to function. You can learn more about these "default claims" in the [session tokens](/docs/backend-requests/resources/session-tokens) documentation.

This guide will show you how to customize a session token to include additional claims that you may need in your application.

> \[!CAUTION]
> The entire session token has a max size of 4kb. Exceeding this size can have adverse effects, including a possible infinite redirect loop for users who exceed this size in Next.js applications.
> It's recommended to move particularly large claims out of the JWT and fetch these using a separate API call from your backend.
> [Learn more](/docs/backend-requests/resources/session-tokens#size-limitations).

<Steps>
  ## Add custom claims to your session token

  1. In the Clerk Dashboard, navigate to the [**Sessions**](https://dashboard.clerk.com/last-active?path=sessions) page.
  2. Under **Customize session token**, in the **Claims** editor, you can add any claim to your session token that you need and select **Save**.

  The following example adds the `fullName` and `primaryEmail` claims to the session token.

  ![Clerk Dashboard showing the custom claim modal](/docs/images/custom-session-token/example.png)

  ## Use the custom claims in your application

  The [`Auth`](/docs/references/backend/types/auth-object) object includes a `sessionClaims` property that contains the custom claims you added to your session token. Accessing the `Auth` object differs depending on the framework you are using. See the [reference doc](/docs/references/backend/types/auth-object) for more information.

  The following example demonstrates how to access the `fullName` and `primaryEmail` claims that were added to the session token in the last step.

  <Tabs items={["Next.js", "Astro", "Express", "React Router", "Remix", "Tanstack React Start"]}>
    <Tab>
      For Next.js, the `Auth` object is accessed using the `auth()` helper in App Router apps and the `getAuth()` function in Pages Router apps. [Learn more about using these helpers](/docs/references/nextjs/read-session-data#server-side).

      <CodeBlockTabs options={["App Router", "Pages Router"]}>
        ```tsx {{ filename: 'app/api/example/route.tsx' }}
        import { auth } from '@clerk/nextjs/server'
        import { NextResponse } from 'next/server'

        export async function GET() {
          const { sessionClaims } = await auth()

          const fullName = sessionClaims?.fullName

          const primaryEmail = sessionClaims?.primaryEmail

          return NextResponse.json({ fullName, primaryEmail })
        }
        ```

        ```tsx {{ filename: 'pages/api/example.ts' }}
        import { getAuth } from '@clerk/nextjs/server'
        import type { NextApiRequest, NextApiResponse } from 'next'

        export default async function handler(req: NextApiRequest, res: NextApiResponse) {
          // Use `getAuth()` to get the user's ID and session claims
          const { sessionClaims } = getAuth(req)

          const fullName = sessionClaims.fullName

          const primaryEmail = sessionClaims.primaryEmail

          return res.status(200).json({ fullName, primaryEmail })
        }
        ```
      </CodeBlockTabs>
    </Tab>

    <Tab>
      For Astro, the `Auth` object is accessed using the `locals.auth()` function. [Learn more about using `locals.auth()`](/docs/references/astro/read-session-data#server-side).

      ```tsx {{ filename: 'src/api/example.ts' }}
      import type { APIRoute } from 'astro'

      export const GET: APIRoute = async ({ locals }) => {
        // Use `locals.auth()` to get the user's ID and session claims
        const { userId, sessionClaims } = await locals.auth()

        // Protect the route by checking if the user is signed in
        if (!userId) {
          return new Response('Unauthorized', { status: 401 })
        }

        const fullName = sessionClaims.fullName

        const primaryEmail = sessionClaims.primaryEmail

        return new Response(JSON.stringify({ fullName, primaryEmail }))
      }
      ```
    </Tab>

    <Tab>
      For Express, the `Auth` object is accessed using the `getAuth()` function. [Learn more about using `getAuth()`](/docs/references/express/overview#get-auth).

      ```js
      import { clerkMiddleware, getAuth, requireAuth } from '@clerk/express'
      import express from 'express'

      const app = express()
      const PORT = 3000

      // Apply `clerkMiddleware()` to all routes
      app.use(clerkMiddleware())

      // Use `getAuth()` to get the session claims
      const getSessionClaims = (req, res, next) => {
        const { sessionClaims } = getAuth(req)

        const fullName = sessionClaims.fullName

        const primaryEmail = sessionClaims.primaryEmail

        return res.status(200).json({ fullName, primaryEmail })
      }

      app.get('/profile', requireAuth(), getSessionClaims)

      // Start the server and listen on the specified port
      app.listen(PORT, () => {
        console.log(`Server is running on http://localhost:${PORT}`)
      })
      ```
    </Tab>

    <Tab>
      For React Router, the `Auth` object is accessed using the `getAuth()` function. [Learn more about using `getAuth()`](/docs/references/react-router/read-session-data#server-side).

      ```tsx {{ filename: 'src/routes/profile.tsx' }}
      import { redirect } from 'react-router'
      import { getAuth } from '@clerk/react-router/ssr.server'
      import { createClerkClient } from '@clerk/react-router/api.server'
      import type { Route } from './+types/profile'

      export async function loader(args: Route.LoaderArgs) {
        // Use `getAuth()` to get the user's ID and session claims
        const { userId, sessionClaims } = await getAuth(args)

        // Protect the route by checking if the user is signed in
        if (!userId) {
          return redirect('/sign-in?redirect_url=' + args.request.url)
        }

        const fullName = sessionClaims.fullName

        const primaryEmail = sessionClaims.primaryEmail

        return {
          fullName: JSON.stringify(fullName),
          primaryEmail: JSON.stringify(primaryEmail),
        }
      }

      export default function Profile({ loaderData }: Route.ComponentProps) {
        return (
          <div>
            <p>Welcome {loaderData.fullName}</p>
            <p>Your email is {loaderData.primaryEmail}</p>
          </div>
        )
      }
      ```
    </Tab>

    <Tab>
      For Remix, the `Auth` object is accessed using the `getAuth()` function. [Learn more about using `getAuth()`](/docs/references/remix/read-session-data#get-auth).

      ```tsx {{ filename: 'routes/profile.tsx' }}
      import { LoaderFunction, redirect } from '@remix-run/node'
      import { getAuth } from '@clerk/remix/ssr.server'
      import { createClerkClient } from '@clerk/remix/api.server'

      export const loader: LoaderFunction = async (args) => {
        // Use `getAuth()` to retrieve the user's ID and session claims
        const { userId, sessionClaims } = await getAuth(args)

        // If there is no userId, then redirect to sign-in route
        if (!userId) {
          return redirect('/sign-in?redirect_url=' + args.request.url)
        }

        const fullName = sessionClaims.fullName

        const primaryEmail = sessionClaims.primaryEmail

        return { fullName, primaryEmail }
      }
      ```
    </Tab>

    <Tab>
      For Tanstack React Start, the `Auth` object is accessed using the `getAuth()` function. [Learn more about using `getAuth()`](/docs/references/tanstack-react-start/read-session-data#server-side).

      ```ts {{ filename: 'app/routes/api/example.ts' }}
      import { getAuth } from '@clerk/tanstack-react-start/server'
      import { json } from '@tanstack/react-start'
      import { createAPIFileRoute } from '@tanstack/react-start/api'

      export const Route = createAPIFileRoute('/api/example')({
        GET: async ({ req, params }) => {
          // Use `getAuth()` to retrieve the user's ID and session claims
          const { userId, sessionClaims } = await getAuth(req)

          // Protect the API route by checking if the user is signed in
          if (!userId) {
            return json({ error: 'Unauthorized' }, { status: 401 })
          }

          const fullName = sessionClaims.fullName

          const primaryEmail = sessionClaims.primaryEmail

          return json({ fullName, primaryEmail })
        },
      })
      ```
    </Tab>
  </Tabs>

  ## Add global TypeScript type for custom session claims

  To get auto-complete and prevent TypeScript errors when working with custom session claims, you can define a global type.

  1. In your application's root folder, add a `types` directory.
  2. Inside of the `types` directory, add a `globals.d.ts` file.
  3. Create the `CustomJwtSessionClaims` interface and declare it globally.
  4. Add the custom claims to the `CustomJwtSessionClaims` interface.

  The following example demonstrates how to add the `fullName` and `primaryEmail` claims to the `CustomJwtSessionClaims` interface.

  ```tsx {{ filename: 'types/globals.d.ts' }}
  export {}

  declare global {
    interface CustomJwtSessionClaims {
      fullName?: string
      primaryEmail?: string
    }
  }
  ```
</Steps>

---
title: JWT templates
description: Learn how to create custom JWT templates to generate JSON Web
  Tokens with Clerk.
lastUpdated: 2025-06-24T16:19:54.000Z
---

> \[!WARNING]
> This guide is for creating custom JWT templates in order to generate JSON Web Tokens with Clerk. If you are looking for how to customize your Clerk-generated session token, refer to the [Customize your session token](/docs/backend-requests/custom-session-token) guide.

Clerk offers the ability to generate [JSON Web Tokens](https://en.wikipedia.org/wiki/JSON_Web_Token) (JWTs). Each JWT, or token, represents a user that is currently signed in to your application.

You can control the claims that will go into these tokens by creating custom **JWT templates** that fit your needs. This enables you to integrate with any third-party services that support authentication with JWTs. An example use case is integrating with a third-party service that is able to consume JWTs, but requires them to be in a particular format.

> \[!WARNING]
> When using custom JWTs, there may be increased latency in token generation due to the additional processing required to fetch and include the custom claim data.

## What is a JWT template?

**JWT templates** are essentially JSON objects that specify claims to be included in the generated tokens, along with their respective values.

Claim values can be either static or dynamic.

Static values can be any of the regular JSON data types (strings, numbers, booleans, arrays, objects, null) and will be included as-is in the tokens.

Dynamic values, also called **shortcodes**, are special strings that will be substituted for their actual values when the tokens are generated. Read more in the [Shortcodes](#shortcodes) section.

The following example shows a template that demonstrates both static values and shortcodes. In this example, the values of the `aud` and `interests` claims are static, and the values of the `name` and `email` claims are dynamic.

```json
{
  "aud": "https://example.com",
  "interests": ["hiking", "knitting"],
  "name": "{{user.first_name}}",
  "surname": "{{user.last_name}}",
  "email": "{{user.primary_email_address}}"
}
```

A token generated using the template above would look something like this:

```json
{
  "aud": "https://example.com",
  "interests": ["hiking", "knitting"],
  "name": "John",
  "surname": null,
  "email": "<EMAIL>"
  // ...plus some automatically-included claims
  // See the following section section for more information
}
```

### Default claims

In every generated token, there are certain claims that are automatically included and cannot be overridden by templates. Clerk calls these "default claims" and you can learn more about them in the [Session tokens](/docs/backend-requests/resources/session-tokens) reference documentation.

For custom JWTs, Clerk automatically includes the following default claims:

```json
{
  // default claims, included automatically
  "azp": "http://localhost:3000",
  "exp": **********,
  "iat": **********,
  "iss": "https://clean-mayfly-62.clerk.accounts.dev",
  "jti": "10db7f531a90cb2faea4",
  "nbf": **********,
  "sub": "user_1deJLArSTiWiF1YdsEWysnhJLLY"
}
```

However, other default claims - such as `sid` (session ID) - are only included in session-bound tokens, not in custom JWTs. Custom JWTs are not inherently tied to a session. Instead, they're designed to be flexible tokens that include the default claims listed above, along with any additional claims you explicitly set in your JWT template. For this reason, session-tied claims like `sid`, `v`, `pla`, or `fea` cannot be included in custom JWTs.

If you need to generate a token that includes both session-bound data (like `sid`) and any additional claims, the recommended approach is to use a custom session token instead of a JWT template. See the [guide on customizing your Clerk session token](/docs/backend-requests/custom-session-token).

### Shortcodes

To include dynamic values in your tokens, you can use shortcodes. Shortcodes are strings that Clerk will replace with the actual values of the corresponding user information when the token is generated.

> \[!NOTE]
> Even though shortcodes are string values, their type in the generated token depends on the original type of the information that's included. For example, `{{user.public_metadata}}` will be substituted for a JSON object, not a string.

#### Metadata in shortcodes

While you can use the `{{user.public_metadata}}` or `{{user.unsafe_metadata}}` shortcodes to include the complete metadata object in the final token, there might be cases where you only need a specific piece of information.

To keep your tokens lean, you can use the dot notation to access nested fields of the metadata object.

Let's assume the user's public metadata are the following:

```json
{
  "interests": ["hiking", "knitting"],
  "addresses": {
    "Home": "2355 Pointe Lane, 56301 Minnesota",
    "Work": "3759 Newton Street, 33487 Florida"
  }
}
```

To access the `interests` array, you would use the shortcode `{{user.public_metadata.interests}}`. To access the `Home` address, you would use `{{user.public_metadata.addresses.Home}}`. See the following example:

```json {{ prettier: false }}
// The template
{
  "likes_to_do": "{{user.public_metadata.interests}}",
  "shipping_address": "{{user.public_metadata.addresses.Home}}"
}

// The generated token
{
  "likes_to_do": ["hiking", "knitting"],
  "shipping_address": "2355 Pointe Lane, 56301 Minnesota"
}
```

#### Interpolation in shortcodes

Shortcodes can be interpolated inside strings.

For example, you could use interpolation to build the user's full name:

```json
{
  "full_name": "{{user.last_name}} {{user.first_name}}"
}
```

Interpolated shortcodes will always result to string values. For example, if the user does not have a last name associated, the above full name value would be `null John`.

#### Conditional expressions in shortcodes

Conditional expressions use the `||` operator and can be used to substitute a default fallback value for shortcodes that would otherwise result in `null` or `false` values.

The format of a conditional expression is the following:

```json
{
  "key": "{{ <operand_1> || <operand_2> || <operand_n> }}"
}
```

The result of a conditional expression is that of the first operand that does not evaluate to `null` or `false` (also known as "falsy"). If all operands of the expression are falsy, the last operand is returned no matter its value. Therefore, you should always place the default value as the last operand. See the following example:

```json
{
  "has_verified_contact_info": "{{user.email_verified || user.phone_number_verified}}",

  // fallback to a string value
  "full_name": "{{user.full_name || 'Awesome User'}}",

  // fallback to a number value
  "age": "{{user.public_metadata.age || user.unsafe_metadata.age || 30 }}"
}
```

For this example, in the case that user:

* has verified their phone number
* has not verified their email
* has not provided their first or last name
* does not have any public or unsafe metadata assigned

Then, the output of the generated token would be:

```json
{
  "has_verified_contact_info": true,
  "full_name": "Awesome User",
  "age": 30
}
```

The rules that govern conditional expressions are as follows:

* The result of an expression is either the first operand that is not falsy, or the last operand (no matter its value).
* String literals should use single quotes (`'`).
* Only strings, booleans and numbers are permitted as literal (i.e. non-shortcodes) operands.

## Create a JWT template

A template consists of the following four properties:

* Template name: a unique identifier for the template. When generating a token, you will have to specify the template to use, using this name. This is a required field.
* Token lifetime: the time in seconds, after which tokens generated with this template will expire. This setting determines the value of the `exp` claim (i.e. `exp=current_time+lifetime`). Default is 60 seconds.
* Token allowed clock skew: the time in seconds, provided as a leeway to account for clock skews between different servers. This setting determines the value of the `nbf` claim (i.e. `nbf=current_time-allowed_clock_skew`). Default is 5 seconds.
* Claims: the actual template that's entered into the JSON editor (see screenshot below). A template is essentially a JSON object that describes what the final token claims will look like (shortcodes can be used here). This is a required field.

To create a JWT template:

1. In the Clerk Dashboard, navigate to the [**JWT templates**](https://dashboard.clerk.com/last-active?path=jwt-templates) page.
2. Select **New template**.
3. You can either select a blank template or choose one of the provided templates.

## Generate a JWT

To generate a token using a template, you can use the `getToken()` method. See the [reference documentation](/docs/references/backend/types/auth-object#get-token) for more information and example usage.

## Complete example

The following example demonstrates the full capabilities of JWT templates, including static claim values, dynamic claim values via shortcodes, and Clerk's "default claims".

Given the following user:

* First name: `Maria`
* Last name: `Doe`
* Profile picture URL: `https://example.com/avatar.jpg`
* Clerk ID: `user_abcdef123456789`
* Email address (verified): `<EMAIL>`
* Phone number: (not provided)
* Public metadata: `{ "profile" : {"interests": ["reading", "climbing"] } }`
* Unsafe metadata: `{ "foo" : { "bar": 42 } }`

And given the following JWT template:

```json
{
  // static values
  "aud": "https://my-site.com",
  "version": 1,
  "foo": { "bar": [1, 2, 3] },

  // dynamic values
  "user_id": "{{user.id}}",
  "avatar": "{{user.image_url}}",
  "full_name": "{{user.last_name}} {{user.first_name}} ",
  "email": "{{user.primary_email_address}}",
  "phone": "{{user.primary_phone_address}}",
  "registration_date": "{{user.created_at}}",
  "likes_to_do": "{{user.public_metadata.profile.interests}}",
  "unsafe_meta": "{{user.unsafe_metadata}}",
  "invalid_shortcode": "{{user.i_dont_exist}}"
}
```

The generated token would look like this:

```json
{
  "aud": "https://my-site.com",
  "version": 1,
  "foo": { "bar": [1, 2, 3] },
  "user_id": "user_abcdef123456789",
  "avatar": "https://example.com/avatar.jpg",
  "full_name": "Doe Maria",
  "email": "<EMAIL>",
  "phone": null,
  "registration_date": **********,
  "likes_to_do": ["reading", "climbing"],
  "unsafe_meta": {
    "foo": {
      "bar": 42
    }
  },
  "invalid_shortcode": null,

  // default claims, included automatically
  "azp": "http://localhost:3000",
  "exp": **********,
  "iat": **********,
  "iss": "https://clean-mayfly-62.clerk.accounts.dev",
  "jti": "10db7f531a90cb2faea4",
  "nbf": **********,
  "sub": "user_1deJLArSTiWiF1YdsEWysnhJLLY"
}
```

---
title: Making requests
description: Learn how to make requests to same-origin and cross-origin endpoints.
lastUpdated: 2025-06-24T16:19:54.000Z
---

To make authenticated requests, the approach differs based on whether your client and server are on the [same origin](#same-origin-requests) or [different origins](#cross-origin-requests).

## Same-origin requests

If your client and server are on the same origin (e.g. making an API call to `foo.com/api` from JavaScript running on `foo.com`), the [session token](/docs/backend-requests/resources/session-tokens) is automatically passed to the backend in a cookie. This means that all requests to same-origin endpoints are **authenticated by default**.

### Vanilla JavaScript

You can use the native browser [Fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API/Using_Fetch) as you normally would and the request will be authenticated.

```js
fetch('/api/foo').then((res) => res.json())
```

### React-based applications

You can use the native browser [Fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API/Using_Fetch) as you normally would and the request will be authenticated. But when a tab loses focus, and data must be fetched in the background, the session token cookie is not automatically included. You'll need to explicitly pass the [session token](/docs/backend-requests/resources/session-tokens) as a Bearer token in the Authorization header.

Use the [`useAuth()`](/docs/hooks/use-auth) hook's `getToken()` method to get the session token. Since `getToken()` returns a Promise, you'll need to await its resolution before making the request.

<Tabs items={["Fetch", "Fetch with SWR", "Fetch with Tanstack Query"]}>
  <Tab>
    ```jsx
    export default async function useFetch() {
      // Use `useAuth()` to access the `getToken()` method
      const { getToken } = useAuth()

      // Use `getToken()` to get the current session token
      const token = await getToken()

      const authenticatedFetch = async (...args) => {
        return fetch(...args, {
          headers: { Authorization: `Bearer ${token}` }, // Include the session token as a Bearer token in the Authorization header
        }).then((res) => res.json())
      }
      return authenticatedFetch
    }
    ```
  </Tab>

  <Tab>
    ```tsx
    import useSWR from 'swr'

    export default async function useClerkSWR(url: string) {
      // Use `useAuth()` to access the `getToken()` method
      const { getToken } = useAuth()

      // Use `getToken()` to get the current session token
      const token = await getToken()

      const fetcher = async (...args: [RequestInfo]) => {
        return fetch(...args, {
          headers: { Authorization: `Bearer ${token}` }, // Include the session token as a Bearer token in the Authorization header
        }).then((res) => res.json())
      }

      return useSWR(url, fetcher)
    }
    ```
  </Tab>

  <Tab>
    When using [Tanstack Query](https://tanstack.com/query/v4/docs/react/overview) (formerly React Query), you'll need a query function that properly handles errors. The native [Fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API/Using_Fetch) doesn't throw errors for non-200 responses, so you'll need to add explicit error handling.

    > \[!NOTE]
    > Your application must be wrapped in a `<QueryClientProvider />` component with a configured `QueryClient` instance. See the [Tanstack Query docs](https://tanstack.com/query/v4/docs/react/quick-start) for setup instructions.

    ```tsx
    import { useQuery } from '@tanstack/react-query'
    import { useAuth } from '@clerk/nextjs'

    // Define your query keys as constants to avoid typos
    export const queryKeys = {
      foo: ['foo'] as const,
      // Add other query keys as needed
    }

    // Define the response type for type safety
    interface FooResponse {
      // Add your response type here
      id: string
      name: string
    }

    export function useFooQuery() {
      // Use `useAuth()` to access the `getToken()` method
      const { getToken } = useAuth()

      return useQuery({
        queryKey: queryKeys.foo,
        queryFn: async (): Promise<FooResponse> => {
          // Use `getToken()` to get the current session token
          const token = await getToken()

          // Make the request
          const response = await fetch('/api/foo', {
            headers: {
              Authorization: `Bearer ${token}`, // Include the session token as a Bearer token in the Authorization header
              'Content-Type': 'application/json',
            },
          })

          if (!response.ok) {
            // Include status code and status text in error message
            throw new Error(`API Error: ${response.status} ${response.statusText}`)
          }

          const data = await response.json()
          return data as FooResponse
        },
        // Add common configuration options
        retry: 2,
        staleTime: 5 * 60 * 1000, // 5 minutes
      })
    }

    // Usage in component:
    function MyComponent() {
      const { data, isLoading, error } = useFooQuery()

      if (isLoading) return <div>Loading...</div>
      if (error) return <div>Error: {error.message}</div>
      if (!data) return null

      return <div>{data.name}</div>
    }
    ```
  </Tab>
</Tabs>

## Cross-origin requests

If your client and server are on different origins (e.g. making an API call to a server on `api.foo.com` from JavaScript running on a client at `foo.com`), the [session token](/docs/backend-requests/resources/session-tokens) needs to be passed as a Bearer token in the Authorization header.

You can retrieve the session token using the `getToken()` method. Since `getToken()` returns a Promise, you'll need to await its resolution before making the request.

### Vanilla JavaScript

In JavaScript applications, use the global `Clerk.session` object to access the [`getToken()`](/docs/references/javascript/session#get-token) method.

```js
(async () => {
  fetch('/api/foo', {
    headers: {
      Authorization: `Bearer ${await Clerk.session.getToken()}`,
    },
  }).then((res) => res.json())
})()
```

### React-based applications

In React-based applications, use the [`useAuth()`](/docs/hooks/use-auth) hook to access the `getToken()` method.

<Tabs items={["Fetch", "Fetch with SWR", "Fetch with Tanstack Query"]}>
  <Tab>
    ```js
    export default async function useFetch() {
      // Use `useAuth()` to access the `getToken()` method
      const { getToken } = useAuth()

      // Use `getToken()` to get the current session token
      const token = await getToken()

      const authenticatedFetch = async (...args) => {
        return fetch(...args, {
          headers: { Authorization: `Bearer ${token}` }, // Include the session token as a Bearer token in the Authorization header
        }).then((res) => res.json())
      }

      return authenticatedFetch
    }
    ```
  </Tab>

  <Tab>
    ```js
    import useSWR from 'swr'
    import { useAuth } from '@clerk/nextjs'

    export default async function useClerkSWR(url) {
      // Use `useAuth()` to access the `getToken()` method
      const { getToken } = useAuth()

      // Use `getToken()` to get the current session token
      const token = await getToken()

      const fetcher = async (...args) => {
        return fetch(...args, {
          headers: { Authorization: `Bearer ${token}` }, // Include the session token as a Bearer token in the Authorization header
        }).then((res) => res.json())
      }

      return useSWR(url, fetcher)
    }
    ```
  </Tab>

  <Tab>
    The following example shows how to use [Tanstack Query](https://tanstack.com/query/v4/docs/react/overview) to create an authenticated query.

    When using [Tanstack Query](https://tanstack.com/query/v4/docs/react/overview) (formerly React Query), you'll need a query function that properly handles errors. The native [Fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API/Using_Fetch) doesn't throw errors for non-200 responses, so you'll need to add explicit error handling.

    > \[!NOTE]
    > Your application must be wrapped in a `<QueryClientProvider />` component with a configured `QueryClient` instance. See the [Tanstack Query docs](https://tanstack.com/query/v4/docs/react/quick-start) for setup instructions.

    ```tsx
    import { useQuery } from '@tanstack/react-query'
    import { useAuth } from '@clerk/nextjs'

    // Define your query keys as constants to avoid typos
    export const queryKeys = {
      foo: ['foo'] as const,
      // Add other query keys as needed
    }

    // Define the response type for type safety
    interface FooResponse {
      // Add your response type here
      id: string
      name: string
    }

    export function useFooQuery() {
      // Use `useAuth()` to access the `getToken()` method
      const { getToken } = useAuth()

      return useQuery({
        queryKey: queryKeys.foo,
        queryFn: async (): Promise<FooResponse> => {
          // Use `getToken()` to get the current session token
          const token = await getToken()

          // Make the request
          const response = await fetch('/api/foo', {
            headers: {
              Authorization: `Bearer ${token}`, // Include the session token as a Bearer token in the Authorization header
              'Content-Type': 'application/json',
            },
          })

          if (!response.ok) {
            // Include status code and status text in error message
            throw new Error(`API Error: ${response.status} ${response.statusText}`)
          }

          const data = await response.json()
          return data as FooResponse
        },
        // Add common configuration options
        retry: 2,
        staleTime: 5 * 60 * 1000, // 5 minutes
      })
    }

    // Usage in component:
    function MyComponent() {
      const { data, isLoading, error } = useFooQuery()

      if (isLoading) return <div>Loading...</div>
      if (error) return <div>Error: {error.message}</div>
      if (!data) return null

      return <div>{data.name}</div>
    }
    ```
  </Tab>
</Tabs>

---
title: Manual JWT verification
description: Learn how to manually verify Clerk-generated session tokens (JWTs).
lastUpdated: 2025-06-24T16:19:54.000Z
---

Your Clerk-generated [session tokens](/docs/backend-requests/resources/session-tokens) are essentially JWTs which are signed using your instance's private key and can be verified using your instance's public key. Depending on your architecture, these tokens will be in your backend requests either via a cookie named `__session` or via the Authorization header.

For every request, you must validate the token to ensure it hasn't expired or been tampered with (i.e., it's authentic and secure). If these validations succeed, then the user is authenticated to your application and should be considered signed in. The `authenticateRequest()` method from the JavaScript Backend SDK handles these validations for you. Alternatively, you can manually verify the token without using the SDK. See the following sections for more information.

## Use `authenticateRequest()` to verify a session token

The [`authenticateRequest()`](/docs/references/backend/authenticate-request) method from the JavaScript Backend SDK accepts the `request` object and authenticates the session token in it.

The following example uses the `authenticateRequest()` method to verify the session token. It also performs networkless authentication by passing `jwtKey`. This verifies if the user is signed into the application. For more information, including usage with higher-level SDKs, see the [`authenticateRequest()` reference](/docs/references/backend/authenticate-request).

```tsx
import { createClerkClient } from '@clerk/backend'

export async function GET(req: Request) {
  const clerkClient = createClerkClient({
    secretKey: process.env.CLERK_SECRET_KEY,
    publishableKey: process.env.CLERK_PUBLISHABLE_KEY,
  })

  const { isSignedIn } = await clerkClient.authenticateRequest(req, {
    jwtKey: process.env.CLERK_JWT_KEY,
    authorizedParties: ['https://example.com'],
  })

  if (!isSignedIn) {
    return Response.json({ status: 401 })
  }

  // Add logic to perform protected actions

  return Response.json({ message: 'This is a reply' })
}
```

## Manually verify a session token

<Steps>
  ### Retrieve the session token

  Retrieve the session token from either `__session` cookie for a same-origin request or from the `Authorization` header for cross-origin requests.

  ### Get your instance's public key

  Use one of the three ways to obtain your public key:

  1. Use the Backend API in JSON Web Key Set (JWKS) format at the following endpoint [https://api.clerk.com/v1/jwks](https://clerk.com/docs/reference/backend-api/tag/JWKS#operation/GetJWKS).
  2. Use your **Frontend API URL** in JWKS format, also known as the **JWKS URL**. The format is your Frontend API URL with `/.well-known/jwks.json` appended to it. Your **Frontend API URL** or **JWKS URL** can be found on the [**API keys**](https://dashboard.clerk.com/last-active?path=api-keys) page in the Clerk Dashboard.
  3. Use your **JWKS Public Key**, which can be found on the [**API keys**](https://dashboard.clerk.com/last-active?path=api-keys) page in the Clerk Dashboard.

  ### Verify the token signature

  To verify the token signature:

  1. Use your instance's public key to verify the token's signature.
  2. Validate that the token isn't expired by checking the `exp` ([expiration time](https://datatracker.ietf.org/doc/html/rfc7519#section-4.1.4)) and `nbf` ([not before](https://datatracker.ietf.org/doc/html/rfc7519#section-4.1.5)) claims.
  3. Validate that the `azp` (authorized parties) claim equals any of your known origins permitted to generate those tokens. For better security, it's highly recommended to explicitly set the `authorizedParties` option when authorizing requests. The value should be a list of domains allowed to make requests to your application. Not setting this value can open your application to [CSRF attacks](https://owasp.org/www-community/attacks/csrf). For example, if you're permitting tokens retrieved from `http://localhost:3000`, then the `azp` claim should equal `http://localhost:3000`. You can also pass an array of strings, such as `['http://localhost:4003', 'https://clerk.dev']`. If the `azp` claim doesn't exist, you can skip this step.

  ### Finished

  If the above process succeeds, the user is considered signed in to your application and authenticated. You can also retrieve the session ID and user ID from of the token's claims.
</Steps>

### Example

The following example manually verifies a session token.

```tsx
import Cookies from 'cookies'
import jwt from 'jsonwebtoken'

export default async function (req: Request, res: Response) {
  // Your public key should be set as an environment variable
  const publicKey = process.env.CLERK_PEM_PUBLIC_KEY
  // Retrieve session token from either `__session` cookie for a same-origin request
  // or from the `Authorization` header for cross-origin requests
  const cookies = new Cookies(req, res)
  const tokenSameOrigin = cookies.get('__session')
  const tokenCrossOrigin = req.headers.authorization

  if (!tokenSameOrigin && !tokenCrossOrigin) {
    res.status(401).json({ error: 'Not signed in' })
    return
  }

  try {
    let decoded
    const options = { algorithms: ['RS256'] } // The algorithm used to sign the token. Optional.
    const permittedOrigins = ['http://localhost:3000', 'https://example.com'] // Replace with your permitted origins

    if (tokenSameOrigin) {
      decoded = jwt.verify(tokenSameOrigin, publicKey, options)
    } else {
      decoded = jwt.verify(tokenCrossOrigin, publicKey, options)
    }

    // Validate the token's expiration (exp) and not before (nbf) claims
    const currentTime = Math.floor(Date.now() / 1000)
    if (decoded.exp < currentTime || decoded.nbf > currentTime) {
      throw new Error('Token is expired or not yet valid')
    }

    // Validate the token's authorized party (azp) claim
    if (decoded.azp && !permittedOrigins.includes(decoded.azp)) {
      throw new Error("Invalid 'azp' claim")
    }

    res.status(200).json({ sessionToken: decoded })
  } catch (error) {
    res.status(400).json({
      error: error.message,
    })
  }
}
```
