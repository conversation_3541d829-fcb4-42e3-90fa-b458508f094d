/// <reference types="vitest" />
import { defineConfig } from 'vitest/config'
import { resolve } from 'path'

export default defineConfig({
  test: {
    // Test environment
    environment: 'node',
    
    // Global setup and teardown
    globalSetup: ['./src/test/setup/global-setup.ts'],
    setupFiles: ['./src/test/setup/test-setup.ts'],
    
    // Test patterns
    include: [
      'src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'tests/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'
    ],
    exclude: [
      'node_modules',
      'dist',
      '.next',
      'coverage',
      'src/test/fixtures/**',
      'src/test/mocks/**'
    ],
    
    // Test timeout
    testTimeout: 30000, // 30 seconds for AI operations
    hookTimeout: 10000, // 10 seconds for setup/teardown
    
    // Coverage configuration
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/**',
        '**/dist/**',
        '**/.next/**',
        'prisma/generated/**',
        'src/app/**/layout.tsx',
        'src/app/**/page.tsx',
        'src/app/**/loading.tsx',
        'src/app/**/error.tsx',
        'src/app/**/not-found.tsx'
      ],
      thresholds: {
        global: {
          branches: 70,
          functions: 70,
          lines: 70,
          statements: 70
        }
      }
    },
    
    // Parallel execution
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        maxThreads: 4,
        minThreads: 1
      }
    },
    
    // Test isolation
    isolate: true,
    
    // Retry configuration
    retry: 2,
    
    // Reporter configuration
    reporters: ['verbose', 'json', 'html'],
    outputFile: {
      json: './test-results/results.json',
      html: './test-results/index.html'
    },
    
    // Watch mode
    watch: false,
    
    // Environment variables for testing
    env: {
      NODE_ENV: 'test',
      DATABASE_URL: 'postgresql://user:password@localhost:5432/buddychip_test',
      DIRECT_URL: 'postgresql://user:password@localhost:5432/buddychip_test',
      CLERK_SECRET_KEY: 'sk_test_mock_key_for_testing',
      OPENROUTER_API_KEY: 'mock_openrouter_key',
      TWITTER_API_KEY: 'mock_twitter_key',
      OPENAI_API_KEY: 'mock_openai_key',
      XAI_API_KEY: 'mock_xai_key',
      PERPLEXITY_API_KEY: 'mock_perplexity_key',
      EXA_API_KEY: 'mock_exa_key',
      UPLOADTHING_TOKEN: 'mock_uploadthing_token',
      ENABLE_PRISMA_QUERY_LOGS: 'false',
      ENABLE_CONTEXT_LOGS: 'false',
      ENABLE_TRPC_REQUEST_LOGS: 'false',
      VERBOSE_LOGGING: 'false'
    }
  },
  
  // Path resolution
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '~': resolve(__dirname, './'),
      '@/test': resolve(__dirname, './src/test')
    }
  },
  
  // Define configuration
  define: {
    'process.env.NODE_ENV': '"test"'
  },
  
  // Esbuild options
  esbuild: {
    target: 'node18'
  }
})
