#!/usr/bin/env node

// Test the working Cookie.fun API endpoints
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const COOKIE_API_KEY = process.env.COOKIE_API_KEY;
const COOKIE_API_BASE_URL = 'https://api.cookie.fun';

console.log('🍪 Testing WORKING Cookie.fun API endpoints...');
console.log('🔑 API Key:', COOKIE_API_KEY);

async function testWorkingEndpoints() {
  if (!COOKIE_API_KEY) {
    console.log('❌ COOKIE_API_KEY not found in environment variables');
    return;
  }

  const testCases = [
    {
      name: 'POST /v3/account (SmokeyTheBera)',
      method: 'POST',
      url: `${COOKIE_API_BASE_URL}/v3/account`,
      body: {
        "username": "SmokeyTheBera"
      }
    },
    {
      name: 'POST /v3/account/smart-followers (SmokeyTheBera)',
      method: 'POST',
      url: `${COOKIE_API_BASE_URL}/v3/account/smart-followers`,
      body: {
        "username": "SmokeyTheBera"
      }
    },
    {
      name: 'POST /v3/project (berachain)',
      method: 'POST',
      url: `${COOKIE_API_BASE_URL}/v3/project`,
      body: {
        "slug": "berachain"
      }
    },
    {
      name: 'POST /v3/project (bitcoin)',
      method: 'POST',
      url: `${COOKIE_API_BASE_URL}/v3/project`,
      body: {
        "slug": "bitcoin"
      }
    }
  ];

  console.log('\n🧪 Testing working endpoints...');
  
  for (const testCase of testCases) {
    try {
      console.log(`\n🔄 Testing: ${testCase.name}`);
      console.log(`📋 Body: ${JSON.stringify(testCase.body, null, 2)}`);
      
      const response = await fetch(testCase.url, {
        method: testCase.method,
        headers: {
          'Authorization': `Bearer ${COOKIE_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testCase.body)
      });

      console.log(`📊 Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Success!`);
        
        // Show response structure
        console.log(`📋 Response keys: ${Object.keys(data).join(', ')}`);
        
        // Show specific data based on endpoint
        if (testCase.url.includes('/account/smart-followers')) {
          if (data.data && Array.isArray(data.data)) {
            console.log(`👥 Smart followers count: ${data.data.length}`);
            if (data.data[0]) {
              console.log(`📈 Top follower: ${data.data[0].username || data.data[0].name || 'Unknown'}`);
            }
          }
        } else if (testCase.url.includes('/account')) {
          if (data.data) {
            console.log(`👤 Account: ${data.data.username || data.data.name || 'Unknown'}`);
            console.log(`👥 Followers: ${data.data.followersCount || 'N/A'}`);
          }
        } else if (testCase.url.includes('/project')) {
          if (data.data) {
            console.log(`🚀 Project: ${data.data.name || data.data.slug || 'Unknown'}`);
            console.log(`🏷️ Symbol: ${data.data.symbol || 'N/A'}`);
            console.log(`🏢 Sector: ${data.data.sector || 'N/A'}`);
          }
        }
        
      } else {
        const errorText = await response.text();
        console.log(`❌ Error: ${errorText}`);
        
        // Try to parse error as JSON
        try {
          const errorJson = JSON.parse(errorText);
          console.log(`📋 Error details: ${JSON.stringify(errorJson, null, 2)}`);
        } catch (e) {
          // Error text is not JSON
        }
      }
      
    } catch (error) {
      console.log(`💥 Exception: ${error.message}`);
    }
    
    // Add delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('\n📋 Test Summary:');
  console.log('These endpoints can be used to build the crypto intelligence features!');
}

testWorkingEndpoints().catch(console.error);
