#!/usr/bin/env node

// Test Cookie.fun mindshare API with exact documentation examples
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const COOKIE_API_KEY = process.env.COOKIE_API_KEY;
const COOKIE_API_BASE_URL = 'https://api.cookie.fun';

console.log('🍪 Testing Cookie.fun API with EXACT documentation examples...');
console.log('🔑 API Key configured:', !!COOKIE_API_KEY);
console.log('🔑 API Key (first 10 chars):', COOKIE_API_KEY?.substring(0, 10) + '...');

async function testExactExamples() {
  if (!COOKIE_API_KEY) {
    console.log('❌ COOKIE_API_KEY not found in environment variables');
    return;
  }

  const url = `${COOKIE_API_BASE_URL}/v3/project/search`;
  
  // Test the EXACT examples from the documentation
  const testCases = [
    {
      name: 'Mindshare Example (EXACT from docs)',
      body: {
        "mindshareTimeframe": "_30Days",
        "sortBy": "Mindshare",
        "sortOrder": "Descending",
        "sectorSlug": "defi"
      }
    },
    {
      name: 'Mindshare Example (without sector)',
      body: {
        "mindshareTimeframe": "_30Days",
        "sortBy": "Mindshare",
        "sortOrder": "Descending"
      }
    },
    {
      name: 'Metrics Example (EXACT from docs)',
      body: {
        "metricType": "Impressions",
        "granulation": "_24Hours",
        "searchQuery": "bitcoin OR $BTC"
      }
    },
    {
      name: 'Project Example (EXACT from docs)',
      body: {
        "searchQuery": "halving",
        "projectSlug": "bitcoin",
        "type": "Original",
        "startDate": "2023-01-01T00:00:00Z",
        "endDate": "2023-01-31T23:59:59Z"
      }
    }
  ];

  console.log('\n🧪 Testing with exact documentation examples...');
  
  for (const testCase of testCases) {
    try {
      console.log(`\n🔄 Testing: ${testCase.name}`);
      console.log(`📋 Body: ${JSON.stringify(testCase.body, null, 2)}`);
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${COOKIE_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testCase.body)
      });

      console.log(`📊 Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Success: Found ${data?.data?.length || 0} projects`);
        
        // Show first project if available
        if (data?.data?.[0]) {
          const project = data.data[0];
          console.log(`📈 Top project: ${project.name || project.slug} (Mindshare: ${project.mindshare || 'N/A'})`);
        }
        
        // Show response structure
        console.log(`📋 Response keys: ${Object.keys(data).join(', ')}`);
        
      } else {
        const errorText = await response.text();
        console.log(`❌ Error: ${errorText}`);
        
        // Try to parse error as JSON
        try {
          const errorJson = JSON.parse(errorText);
          console.log(`📋 Error details: ${JSON.stringify(errorJson, null, 2)}`);
        } catch (e) {
          // Error text is not JSON
        }
      }
      
    } catch (error) {
      console.log(`💥 Exception: ${error.message}`);
    }
    
    // Add delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
  
  console.log('\n📋 Test Summary:');
  console.log('If all tests return 404, the API endpoints may have changed or the service may be down.');
  console.log('If some tests work, we can identify which endpoint format is correct.');
}

testExactExamples().catch(console.error);
