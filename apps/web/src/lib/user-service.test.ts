/**
 * User Service Unit Tests
 * 
 * Comprehensive tests for user management functionality
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { getOrCreateUser, getUserById, updateUser, getUserStats, canUserUseFeature, logUsage } from './user-service'
import { mockPrismaClient, mockClerkAuth, mockEnvironmentVariables, clearAllMocks } from '../test/utils/mocks'

// Mock dependencies
vi.mock('./db-utils')
vi.mock('@clerk/nextjs/server')

describe('User Service', () => {
  let mockPrisma: any
  
  beforeEach(() => {
    clearAllMocks()
    mockPrisma = mockPrismaClient()
    mockEnvironmentVariables()
    
    console.log('🧪 User Service Test: Setup complete')
  })
  
  describe('getOrCreateUser', () => {
    it('should return existing user when found', async () => {
      // Arrange
      const userId = 'test-user-123'
      const existingUser = {
        id: userId,
        email: '<EMAIL>',
        name: 'Test User',
        planId: 'test-reply-guy',
        plan: {
          id: 'test-reply-guy',
          name: 'reply-guy',
          features: [
            { feature: 'AI_CALLS', limit: 100 }
          ]
        },
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      mockPrisma.user.findUnique.mockResolvedValue(existingUser)
      
      // Act
      const result = await getOrCreateUser(userId)
      
      // Assert
      expect(result).toEqual(existingUser)
      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
        include: {
          plan: {
            include: {
              features: true
            }
          }
        }
      })
      expect(mockPrisma.user.create).not.toHaveBeenCalled()
      
      console.log('✅ User Service Test: Existing user retrieval works')
    })
    
    it('should create new user when not found', async () => {
      // Arrange
      const userId = 'test-user-new'
      const userData = {
        email: '<EMAIL>',
        name: 'New User',
        avatar: 'https://example.com/avatar.jpg'
      }
      
      const defaultPlan = {
        id: 'test-reply-guy',
        name: 'reply-guy',
        features: [
          { feature: 'AI_CALLS', limit: 100 }
        ]
      }
      
      const newUser = {
        id: userId,
        ...userData,
        planId: defaultPlan.id,
        plan: defaultPlan,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      // Mock user not found, then plan found, then user created
      mockPrisma.user.findUnique.mockResolvedValue(null)
      mockPrisma.subscriptionPlan.findFirst.mockResolvedValue(defaultPlan)
      mockPrisma.user.create.mockResolvedValue(newUser)
      
      // Act
      const result = await getOrCreateUser(userId, userData)
      
      // Assert
      expect(result).toEqual(newUser)
      expect(mockPrisma.user.create).toHaveBeenCalledWith({
        data: {
          id: userId,
          email: userData.email,
          name: userData.name,
          avatar: userData.avatar,
          planId: defaultPlan.id,
          lastActiveAt: expect.any(Date)
        },
        include: {
          plan: {
            include: {
              features: true
            }
          }
        }
      })
      
      console.log('✅ User Service Test: New user creation works')
    })
    
    it('should handle database connection failure', async () => {
      // Arrange
      const userId = 'test-user-fail'
      
      // Mock database connection failure
      vi.mocked(require('./db-utils').testDatabaseConnection).mockResolvedValue(false)
      
      // Act & Assert
      await expect(getOrCreateUser(userId)).rejects.toThrow(
        'Database connection failed. Please check your DATABASE_URL configuration.'
      )
      
      console.log('✅ User Service Test: Database failure handling works')
    })
    
    it('should update lastActiveAt for existing user', async () => {
      // Arrange
      const userId = 'test-user-active'
      const existingUser = {
        id: userId,
        email: '<EMAIL>',
        name: 'Test User',
        planId: 'test-reply-guy',
        plan: { id: 'test-reply-guy', name: 'reply-guy', features: [] },
        lastActiveAt: new Date(Date.now() - 86400000) // 1 day ago
      }
      
      mockPrisma.user.findUnique.mockResolvedValue(existingUser)
      mockPrisma.user.update.mockResolvedValue(existingUser)
      
      // Act
      await getOrCreateUser(userId)
      
      // Assert
      expect(mockPrisma.user.update).toHaveBeenCalledWith({
        where: { id: userId },
        data: { lastActiveAt: expect.any(Date) }
      })
      
      console.log('✅ User Service Test: Last active update works')
    })
  })
  
  describe('getUserById', () => {
    it('should return user when found', async () => {
      // Arrange
      const userId = 'test-user-123'
      const user = {
        id: userId,
        email: '<EMAIL>',
        plan: { id: 'test-reply-guy', features: [] }
      }
      
      mockPrisma.user.findUnique.mockResolvedValue(user)
      
      // Act
      const result = await getUserById(userId)
      
      // Assert
      expect(result).toEqual(user)
      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
        include: {
          plan: {
            include: {
              features: true
            }
          }
        }
      })
      
      console.log('✅ User Service Test: Get user by ID works')
    })
    
    it('should return null when user not found', async () => {
      // Arrange
      const userId = 'non-existent-user'
      mockPrisma.user.findUnique.mockResolvedValue(null)
      
      // Act
      const result = await getUserById(userId)
      
      // Assert
      expect(result).toBeNull()
      
      console.log('✅ User Service Test: Non-existent user handling works')
    })
  })
  
  describe('updateUser', () => {
    it('should update user data and lastActiveAt', async () => {
      // Arrange
      const userId = 'test-user-123'
      const updateData = {
        email: '<EMAIL>',
        name: 'Updated Name',
        avatar: 'https://example.com/new-avatar.jpg'
      }
      
      const updatedUser = {
        id: userId,
        ...updateData,
        lastActiveAt: new Date()
      }
      
      mockPrisma.user.update.mockResolvedValue(updatedUser)
      
      // Act
      const result = await updateUser(userId, updateData)
      
      // Assert
      expect(result).toEqual(updatedUser)
      expect(mockPrisma.user.update).toHaveBeenCalledWith({
        where: { id: userId },
        data: {
          ...updateData,
          lastActiveAt: expect.any(Date)
        }
      })
      
      console.log('✅ User Service Test: User update works')
    })
  })
  
  describe('canUserUseFeature', () => {
    it('should allow feature usage when under limit', async () => {
      // Arrange
      const userId = 'test-user-123'
      const feature = 'AI_CALLS'
      
      const user = {
        id: userId,
        plan: {
          features: [
            { feature: 'AI_CALLS', limit: 100 }
          ]
        }
      }
      
      mockPrisma.user.findUnique.mockResolvedValue(user)
      mockPrisma.usageLog.count.mockResolvedValue(50) // Under limit
      
      // Act
      const result = await canUserUseFeature(userId, feature)
      
      // Assert
      expect(result.allowed).toBe(true)
      expect(result.currentUsage).toBe(50)
      expect(result.limit).toBe(100)
      
      console.log('✅ User Service Test: Feature usage check works')
    })
    
    it('should deny feature usage when over limit', async () => {
      // Arrange
      const userId = 'test-user-123'
      const feature = 'AI_CALLS'
      
      const user = {
        id: userId,
        plan: {
          features: [
            { feature: 'AI_CALLS', limit: 100 }
          ]
        }
      }
      
      mockPrisma.user.findUnique.mockResolvedValue(user)
      mockPrisma.usageLog.count.mockResolvedValue(100) // At limit
      
      // Act
      const result = await canUserUseFeature(userId, feature)
      
      // Assert
      expect(result.allowed).toBe(false)
      expect(result.currentUsage).toBe(100)
      expect(result.limit).toBe(100)
      
      console.log('✅ User Service Test: Feature limit enforcement works')
    })
    
    it('should handle non-existent user gracefully', async () => {
      // Arrange
      const userId = 'non-existent-user'
      const feature = 'AI_CALLS'
      
      mockPrisma.user.findUnique.mockResolvedValue(null)
      
      // Act
      const result = await canUserUseFeature(userId, feature)
      
      // Assert
      expect(result.allowed).toBe(false)
      expect(result.currentUsage).toBe(0)
      expect(result.limit).toBe(0)
      
      console.log('✅ User Service Test: Non-existent user feature check works')
    })
  })
  
  describe('logUsage', () => {
    it('should create usage log entry', async () => {
      // Arrange
      const userId = 'test-user-123'
      const feature = 'AI_CALLS'
      const metadata = { model: 'gemini25Flash', tokens: 150 }
      
      const usageLog = {
        id: 'usage-123',
        userId,
        feature,
        metadata,
        createdAt: new Date()
      }
      
      mockPrisma.usageLog.create.mockResolvedValue(usageLog)
      
      // Act
      const result = await logUsage(userId, feature, 1, metadata)
      
      // Assert
      expect(result).toEqual(usageLog)
      expect(mockPrisma.usageLog.create).toHaveBeenCalledWith({
        data: {
          userId,
          feature,
          amount: 1,
          metadata,
          billingPeriod: expect.any(String)
        }
      })
      
      console.log('✅ User Service Test: Usage logging works')
    })
  })
})
