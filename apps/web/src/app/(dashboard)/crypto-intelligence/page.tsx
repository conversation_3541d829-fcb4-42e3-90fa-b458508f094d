"use client"

import type React from "react"
import { useState, useEffect, useCallback } from "react"
import { useUser } from '@clerk/nextjs'
import { redirect } from 'next/navigation'
import { RefreshCw } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import AuthenticatedNavbar from "@/components/authenticated-navbar"
import ShardLoadingAnimation from "@/components/ui/shard-loading-animation"
import SectorOverviewCard from "@/components/crypto/SectorOverviewCard"
import SmartFollowersPanel from "@/components/crypto/SmartFollowersPanel"
import TrendingProjectsFeed from "@/components/crypto/TrendingProjectsFeed"
import { trpc } from "@/utils/trpc"
import { toast } from "sonner"

export default function CryptoIntelligencePage() {
  const { user, isLoaded } = useUser()
  const [selectedSector, setSelectedSector] = useState<string>("")
  const [timeframe, setTimeframe] = useState<"_7Days" | "_30Days">("_7Days")
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())

  // Get tRPC utils for cache management
  const utils = trpc.useUtils()

  // 🎯 OPTIMIZED SECTORS QUERY
  const { 
    data: sectors, 
    isLoading: sectorsLoading,
    error: sectorsError,
    refetch: refetchSectors,
    isFetching: sectorsFetching
  } = trpc.crypto.getSectors.useQuery(undefined, {
    // Optimized Cache Strategy (leveraging server-side caching)
    staleTime: 60 * 60 * 1000, // Consider fresh for 1 hour (server has 1hr cache)  
    gcTime: 4 * 60 * 60 * 1000, // Keep in memory for 4 hours (renamed from cacheTime)
    
    // Minimal Refetch Strategy (sectors are stable data)
    refetchOnMount: false, // Don't refetch on mount if data exists
    refetchOnWindowFocus: false, // Don't refetch on window focus
    refetchOnReconnect: false, // Don't refetch on reconnect
    
    // Error Handling
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  })

  // 🚀 OPTIMIZED TRENDING PROJECTS QUERY  
  const {
    data: trendingProjects,
    isLoading: trendingLoading,
    refetch: refetchTrending,
    error: trendingError,
    isFetching: trendingFetching
  } = trpc.crypto.getTrendingProjects.useQuery({
    sectorSlug: selectedSector || undefined,
    timeframe,
    limit: 20,
  }, {
    // Dependency Management
    enabled: !!sectors && !sectorsLoading, // Only fetch after sectors are loaded
    
    // Optimized Cache Strategy (leveraging server-side caching)
    staleTime: 15 * 60 * 1000, // Consider fresh for 15 minutes (server has cache)
    gcTime: 30 * 60 * 1000, // Keep in cache for 30 minutes (renamed from cacheTime)
    
    // Refetch Strategy (reduced to prevent duplicates)
    refetchOnMount: 'always', // Always refetch on mount for fresh data
    refetchOnWindowFocus: false, // Prevent too frequent updates
    refetchOnReconnect: true,
    
    // Error Handling
    retry: 2,
    retryDelay: 1000,
  })

  // 📊 COMPUTED LOADING STATES (moved before useEffects that depend on them)
  const isInitialLoading = sectorsLoading && trendingLoading
  const hasAnyData = sectors || trendingProjects
  const hasError = sectorsError || trendingError
  const isDataStale = Date.now() - lastRefresh.getTime() > 10 * 60 * 1000 // 10 minutes

  // 🔄 MANUAL REFRESH FUNCTION
  const handleRefreshAll = useCallback(async () => {
    if (isRefreshing) return // Prevent multiple simultaneous refreshes
    
    setIsRefreshing(true)
    
    try {
      console.log('🔄 Refreshing all crypto intelligence data...')
      
      // Invalidate all relevant queries
      await utils.crypto.getSectors.invalidate()
      await utils.crypto.getTrendingProjects.invalidate()
      
      // Force refetch both queries in parallel
      const refreshPromises = [
        refetchSectors(),
        refetchTrending()
      ]
      
      await Promise.all(refreshPromises)
      
      setLastRefresh(new Date())
      toast.success("🎉 Crypto intelligence data refreshed!")
      
    } catch (error) {
      console.error('Failed to refresh crypto data:', error)
      toast.error("❌ Failed to refresh data. Please try again.")
    } finally {
      setIsRefreshing(false)
    }
  }, [isRefreshing, utils, refetchSectors, refetchTrending])

  // 🔧 AUTO-REFRESH ON DEPENDENCY CHANGE
  // REMOVED: tRPC automatically refetches when query inputs change (selectedSector, timeframe)
  // Manual refetch here causes duplicate requests

  // ⏰ INTELLIGENT BACKGROUND REFRESH (reduced frequency due to server-side caching)
  useEffect(() => {
    // Reduced refresh frequency since server has multi-layer caching
    const refreshInterval = hasAnyData ? 30 * 60 * 1000 : 2 * 60 * 1000 // 30min with data, 2min without data
    
    const interval = setInterval(() => {
      if (!isRefreshing && !trendingFetching && !sectorsFetching) {
        if (!hasAnyData) {
          console.log('🚨 No data available - refresh triggered')
        } else {
          console.log('⏰ Background refresh triggered (cached data likely available)')
        }
        handleRefreshAll()
      }
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [handleRefreshAll, isRefreshing, trendingFetching, sectorsFetching, hasAnyData])

  // 🔄 RETRY LOGIC FOR FAILED REQUESTS
  useEffect(() => {
    if (hasError && !isRefreshing && !isInitialLoading) {
      console.log('❌ Error detected - scheduling retry in 5 seconds')
      const retryTimeout = setTimeout(() => {
        handleRefreshAll()
      }, 5000) // Retry after 5 seconds on error

      return () => clearTimeout(retryTimeout)
    }
  }, [hasError, isRefreshing, isInitialLoading, handleRefreshAll])

  if (!isLoaded) {
    return <div className="min-h-screen bg-app-background flex items-center justify-center">
      <ShardLoadingAnimation size={80} />
    </div>
  }

  if (!user) {
    redirect('/sign-in')
  }

  return (
    <div className="min-h-screen p-3 sm:p-4 md:p-6 lg:p-8 font-sans bg-app-background text-app-headline">
      <header className="text-center mb-6 sm:mb-8">
        <div className="flex items-center justify-center gap-4 mb-4">
          <h1 className="text-[clamp(1.75rem,5vw,3rem)] font-bold tracking-wider text-app-headline">
            CRYPTO INTELLIGENCE
          </h1>
          <Button
            onClick={handleRefreshAll}
            disabled={isRefreshing || isInitialLoading}
            variant="outline"
            size="sm"
            className="border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary min-h-[44px] min-w-[44px] transition-all"
          >
            <RefreshCw className={`w-4 h-4 ${isRefreshing || sectorsFetching || trendingFetching ? 'animate-spin' : ''}`} />
          </Button>
        </div>
        <div className="space-y-2">
          <p className="text-sm sm:text-base text-app-headline/70 max-w-3xl mx-auto">
            Real-time crypto market intelligence, competitive analysis, and smart account discovery powered by Cookie.fun
          </p>
          {/* Data Freshness Indicator */}
          <div className="flex items-center justify-center gap-2 text-xs text-app-headline/50">
            <div className={`w-2 h-2 rounded-full ${isDataStale ? 'bg-yellow-500' : 'bg-green-500'} ${(sectorsFetching || trendingFetching) ? 'animate-pulse' : ''}`}></div>
            <span>
              Last updated: {lastRefresh.toLocaleTimeString()} 
              {isDataStale && ' (Data may be stale)'}
              {(sectorsFetching || trendingFetching) && ' (Updating...)'}
            </span>
          </div>
        </div>
      </header>

      <AuthenticatedNavbar currentPage="crypto-intelligence" />

      <main className="space-y-6 sm:space-y-8">
        {/* Error Boundary */}
        {hasError && (
          <Card className="bg-red-50 border-red-200 text-red-800 shadow-sm">
            <CardContent className="p-6 text-center">
              <div className="space-y-3">
                <h3 className="font-semibold">⚠️ Failed to Load Crypto Data</h3>
                <p className="text-sm">
                  {sectorsError?.message || trendingError?.message || 'Unable to fetch latest crypto intelligence data'}
                </p>
                <div className="flex items-center justify-center gap-2 text-xs text-red-600/70">
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                  <span>Auto-retrying in 5 seconds...</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Main Intelligence Dashboard */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - Sector Overview */}
          {isInitialLoading ? (
            <Card className="bg-app-card border-app-stroke rounded-lg">
              <CardContent className="p-6 text-center space-y-4">
                <div className="animate-pulse space-y-3">
                  <div className="h-4 bg-app-stroke/30 rounded w-3/4 mx-auto"></div>
                  <div className="h-8 bg-app-stroke/30 rounded"></div>
                  <div className="h-4 bg-app-stroke/30 rounded w-1/2 mx-auto"></div>
                </div>
                <p className="text-app-headline/60 text-sm">Loading sector overview...</p>
              </CardContent>
            </Card>
          ) : hasAnyData ? (
            <SectorOverviewCard
              sectors={sectors}
              trendingProjects={trendingProjects}
              trendingLoading={trendingLoading || trendingFetching}
              selectedSector={selectedSector}
              setSelectedSector={setSelectedSector}
              timeframe={timeframe}
              setTimeframe={setTimeframe}
              refetchTrending={refetchTrending}
            />
          ) : (
            <Card className="bg-app-card border-app-stroke rounded-lg">
              <CardContent className="p-6 text-center space-y-3">
                <div className="text-app-headline/60">No sector data available</div>
                <div className="flex items-center justify-center gap-2 text-xs text-app-headline/40">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <span>Auto-refreshing every 30 seconds...</span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Right Column - Trending Projects */}
          {isInitialLoading ? (
            <Card className="bg-app-card border-app-stroke rounded-lg">
              <CardContent className="p-6 text-center space-y-4">
                <div className="animate-pulse space-y-3">
                  <div className="h-4 bg-app-stroke/30 rounded w-3/4 mx-auto"></div>
                  <div className="space-y-2">
                    {[1,2,3].map(i => (
                      <div key={i} className="h-12 bg-app-stroke/30 rounded"></div>
                    ))}
                  </div>
                </div>
                <p className="text-app-headline/60 text-sm">Loading trending projects...</p>
              </CardContent>
            </Card>
          ) : hasAnyData ? (
            <TrendingProjectsFeed
              sectors={sectors}
              trendingProjects={trendingProjects}
              trendingLoading={trendingLoading || trendingFetching}
              trendingError={trendingError}
              selectedSector={selectedSector}
              setSelectedSector={setSelectedSector}
              timeframe={timeframe}
              setTimeframe={setTimeframe}
              refetchTrending={refetchTrending}
            />
          ) : (
            <Card className="bg-app-card border-app-stroke rounded-lg">
              <CardContent className="p-6 text-center space-y-3">
                <div className="text-app-headline/60">No trending projects available</div>
                <div className="flex items-center justify-center gap-2 text-xs text-app-headline/40">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <span>Auto-refreshing every 30 seconds...</span>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Data Quality Indicator */}
        {hasAnyData && (
          <Card className="bg-app-background/50 border-app-stroke/30">
            <CardContent className="p-4">
              <div className="flex items-center justify-between text-xs text-app-headline/50">
                <div className="flex items-center gap-2">
                  <span>📊 Data Quality:</span>
                  <span className={sectors ? 'text-green-600' : 'text-red-600'}>
                    Sectors {sectors ? '✓' : '✗'}
                  </span>
                  <span className={trendingProjects ? 'text-green-600' : 'text-red-600'}>
                    Trending {trendingProjects ? '✓' : '✗'}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span>🔄 Auto-refresh: {hasAnyData ? 'Every 10min' : 'Every 30sec'}</span>
                  {(sectorsFetching || trendingFetching) && (
                    <span className="text-blue-600 animate-pulse">● Updating</span>
                  )}
                  {hasError && (
                    <span className="text-red-600 animate-pulse">● Retrying</span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Smart Followers Discovery - Full Width */}
        <SmartFollowersPanel />
      </main>
    </div>
  )
}