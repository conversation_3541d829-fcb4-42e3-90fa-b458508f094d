'use client'

import { <PERSON><PERSON>, Search, LayoutDashboard, <PERSON>u, X, Bar<PERSON><PERSON>3 } from "lucide-react"
import ProfileDropdown from "./profile-dropdown"
import Link from "next/link"
import Logo from "./logo"
import { useState } from "react"
import { Button } from "@/components/ui/button"

interface AuthenticatedNavbarProps {
  currentPage?: 'dashboard' | 'profile' | 'reply-guy' | 'copium' | 'crypto-intelligence'
}

export default function AuthenticatedNavbar({ currentPage = 'dashboard' }: AuthenticatedNavbarProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  console.log('🔍 AuthenticatedNavbar: Rendering with currentPage:', currentPage);

  const navigationItems = [
    {
      href: '/dashboard',
      label: 'DASHBOARD',
      icon: LayoutDashboard,
      key: 'dashboard'
    },
    {
      href: '/crypto-intelligence',
      label: 'CRYPTO INTEL',
      icon: BarChart3,
      key: 'crypto-intelligence'
    },
    {
      href: '/reply-guy',
      label: 'REPLY GUY',
      icon: Bo<PERSON>,
      key: 'reply-guy'
    },
    {
      href: '/copium',
      label: 'COPIUM',
      icon: Search,
      key: 'copium'
    }
  ]

  return (
    <nav className="mb-6 sm:mb-8 p-3 sm:p-4 border border-app-stroke rounded-lg bg-app-card shadow-md">
      <div className="container mx-auto flex justify-between items-center">
        <Logo
          href="/dashboard"
          size={180}
          showText={true}
          className="w-[120px] sm:w-[140px] md:w-[180px] h-auto"
        />

        {/* Desktop Navigation */}
        <div className="hidden md:flex space-x-4 lg:space-x-6 text-sm lg:text-base items-center">
          {navigationItems.map((item) => (
            <Link
              key={item.key}
              href={item.href}
              className={`transition-colors flex items-center min-h-[44px] px-3 py-2 rounded-md ${
                currentPage === item.key
                  ? 'text-app-main font-semibold bg-app-main/10'
                  : 'text-app-headline hover:text-app-main hover:bg-app-main/5'
              }`}
            >
              <item.icon className="w-4 h-4 mr-2" />
              {item.label}
            </Link>
          ))}
          <ProfileDropdown />
        </div>

        {/* Mobile Menu Button */}
        <div className="md:hidden flex items-center gap-2">
          <ProfileDropdown />
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="min-h-[44px] min-w-[44px] p-2"
          >
            {isMobileMenuOpen ? (
              <X className="h-5 w-5" />
            ) : (
              <Menu className="h-5 w-5" />
            )}
          </Button>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden mt-4 pt-4 border-t border-app-stroke">
          <div className="flex flex-col space-y-2">
            {navigationItems.map((item) => (
              <Link
                key={item.key}
                href={item.href}
                onClick={() => setIsMobileMenuOpen(false)}
                className={`transition-colors flex items-center min-h-[48px] px-4 py-3 rounded-md text-base ${
                  currentPage === item.key
                    ? 'text-app-main font-semibold bg-app-main/10'
                    : 'text-app-headline hover:text-app-main hover:bg-app-main/5'
                }`}
              >
                <item.icon className="w-5 h-5 mr-3" />
                {item.label}
              </Link>
            ))}
          </div>
        </div>
      )}
    </nav>
  )
}