/**
 * Database Test Utilities
 * 
 * Helper functions for database operations during testing
 */

import { PrismaClient } from '../../../prisma/generated'

// Create a test-specific Prisma client
export const testPrisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  },
  log: process.env.ENABLE_PRISMA_QUERY_LOGS === 'true' ? ['query', 'info', 'warn', 'error'] : []
})

/**
 * Clean up all test data from database
 */
export async function cleanupDatabase(): Promise<void> {
  try {
    console.log('🧹 Database: Cleaning up test data...')
    
    // Delete in correct order to respect foreign key constraints
    await testPrisma.$transaction([
      testPrisma.usageLog.deleteMany({
        where: {
          userId: {
            startsWith: 'test-'
          }
        }
      }),
      testPrisma.aIResponse.deleteMany({
        where: {
          userId: {
            startsWith: 'test-'
          }
        }
      }),
      testPrisma.mention.deleteMany({
        where: {
          userId: {
            startsWith: 'test-'
          }
        }
      }),
      testPrisma.monitoredAccount.deleteMany({
        where: {
          userId: {
            startsWith: 'test-'
          }
        }
      }),
      testPrisma.image.deleteMany({
        where: {
          userId: {
            startsWith: 'test-'
          }
        }
      }),
      testPrisma.user.deleteMany({
        where: {
          id: {
            startsWith: 'test-'
          }
        }
      })
    ])
    
    console.log('✅ Database: Cleanup complete')
  } catch (error) {
    console.error('❌ Database: Cleanup failed:', error)
    throw error
  }
}

/**
 * Reset test data to known state
 */
export async function resetTestData(): Promise<void> {
  try {
    console.log('🔄 Database: Resetting test data...')
    
    // Clean up first
    await cleanupDatabase()
    
    // Create fresh test data
    await seedTestData()
    
    console.log('✅ Database: Reset complete')
  } catch (error) {
    console.error('❌ Database: Reset failed:', error)
    throw error
  }
}

/**
 * Seed basic test data
 */
export async function seedTestData(): Promise<void> {
  try {
    console.log('🌱 Database: Seeding test data...')
    
    // Ensure test plans exist
    await testPrisma.subscriptionPlan.upsert({
      where: { id: 'test-reply-guy' },
      update: {},
      create: {
        id: 'test-reply-guy',
        name: 'reply-guy',
        displayName: 'Reply Guy',
        description: 'Test plan for Reply Guy',
        price: 2000,
        isActive: true
      }
    })
    
    await testPrisma.subscriptionPlan.upsert({
      where: { id: 'test-reply-god' },
      update: {},
      create: {
        id: 'test-reply-god',
        name: 'reply-god',
        displayName: 'Reply God',
        description: 'Test plan for Reply God',
        price: 5000,
        isActive: true
      }
    })
    
    // Ensure test plan features exist
    const planFeatures = [
      { planId: 'test-reply-guy', feature: 'AI_CALLS', limit: 100 },
      { planId: 'test-reply-guy', feature: 'IMAGES', limit: 20 },
      { planId: 'test-reply-guy', feature: 'ACCOUNTS', limit: 3 },
      { planId: 'test-reply-god', feature: 'AI_CALLS', limit: 500 },
      { planId: 'test-reply-god', feature: 'IMAGES', limit: 50 },
      { planId: 'test-reply-god', feature: 'ACCOUNTS', limit: 10 }
    ]
    
    for (const feature of planFeatures) {
      await testPrisma.planFeature.upsert({
        where: {
          planId_feature: {
            planId: feature.planId,
            feature: feature.feature as any
          }
        },
        update: { limit: feature.limit },
        create: feature as any
      })
    }
    
    // Ensure test AI models exist
    await testPrisma.aIModel.upsert({
      where: { id: 'test-gemini-flash' },
      update: {},
      create: {
        id: 'test-gemini-flash',
        name: 'gemini25Flash',
        displayName: 'Gemini 2.5 Flash',
        provider: 'google',
        isActive: true,
        description: 'Test AI model',
        modelId: 'test-model',
        costTier: 'low',
        speed: 'fast'
      }
    })
    
    // Ensure test personality profiles exist
    await testPrisma.personalityProfile.upsert({
      where: { id: 'test-professional' },
      update: {},
      create: {
        id: 'test-professional',
        name: 'Professional',
        description: 'Professional and courteous responses',
        systemPrompt: 'You are a professional AI assistant.',
        isActive: true,
        isDefault: true
      }
    })
    
    console.log('✅ Database: Seeding complete')
  } catch (error) {
    console.error('❌ Database: Seeding failed:', error)
    throw error
  }
}

/**
 * Create a test user with plan
 */
export async function createTestUser(userData: {
  id?: string
  email?: string
  name?: string
  planId?: string
} = {}): Promise<any> {
  const defaultData = {
    id: `test-user-${Date.now()}`,
    email: '<EMAIL>',
    name: 'Test User',
    planId: 'test-reply-guy',
    ...userData
  }
  
  return testPrisma.user.create({
    data: {
      ...defaultData,
      lastActiveAt: new Date()
    },
    include: {
      plan: {
        include: {
          features: true
        }
      }
    }
  })
}

/**
 * Create a test monitored account
 */
export async function createTestAccount(accountData: {
  userId: string
  twitterHandle?: string
  displayName?: string
  twitterUserId?: string
} = { userId: 'test-user-123' }): Promise<any> {
  const defaultData = {
    twitterHandle: 'testhandle',
    displayName: 'Test Handle',
    twitterUserId: 'twitter-123',
    isActive: true,
    ...accountData
  }
  
  return testPrisma.monitoredAccount.create({
    data: defaultData
  })
}

/**
 * Create a test mention
 */
export async function createTestMention(mentionData: {
  userId: string
  accountId?: string
  content?: string
  authorHandle?: string
} = { userId: 'test-user-123' }): Promise<any> {
  const defaultData = {
    id: `test-mention-${Date.now()}`,
    accountId: 'test-account-123',
    link: 'https://x.com/test/status/123',
    content: 'This is a test mention',
    authorHandle: 'testuser',
    authorName: 'Test User',
    authorId: 'test-author-id',
    mentionedAt: new Date(),
    processed: false,
    isUserTweet: false,
    ...mentionData
  }
  
  return testPrisma.mention.create({
    data: defaultData
  })
}

/**
 * Create a test AI response
 */
export async function createTestAIResponse(responseData: {
  userId: string
  mentionId: string
  content?: string
  model?: string
} = { userId: 'test-user-123', mentionId: 'test-mention-123' }): Promise<any> {
  const defaultData = {
    id: `test-response-${Date.now()}`,
    content: 'This is a test AI response',
    model: 'gemini25Flash',
    confidence: 0.95,
    used: false,
    processingTime: BigInt(1500),
    version: '1.0',
    ...responseData
  }
  
  return testPrisma.aIResponse.create({
    data: defaultData
  })
}

/**
 * Get test database statistics
 */
export async function getTestDatabaseStats(): Promise<{
  users: number
  accounts: number
  mentions: number
  responses: number
}> {
  const [users, accounts, mentions, responses] = await Promise.all([
    testPrisma.user.count({
      where: { id: { startsWith: 'test-' } }
    }),
    testPrisma.monitoredAccount.count({
      where: { userId: { startsWith: 'test-' } }
    }),
    testPrisma.mention.count({
      where: { userId: { startsWith: 'test-' } }
    }),
    testPrisma.aIResponse.count({
      where: { userId: { startsWith: 'test-' } }
    })
  ])
  
  return { users, accounts, mentions, responses }
}

/**
 * Disconnect test database
 */
export async function disconnectTestDatabase(): Promise<void> {
  await testPrisma.$disconnect()
}
