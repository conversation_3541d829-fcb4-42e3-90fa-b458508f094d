/**
 * Mock Utilities
 * 
 * Helper functions for creating and managing mocks during testing
 */

import { vi } from 'vitest'

/**
 * Clear all mocks
 */
export function clearAllMocks(): void {
  vi.clearAllMocks()
}

/**
 * Reset all mocks
 */
export function resetAllMocks(): void {
  vi.resetAllMocks()
}

/**
 * Mock Clerk authentication
 */
export function mockClerkAuth(userData: {
  userId?: string
  sessionId?: string
  email?: string
  firstName?: string
  lastName?: string
} = {}) {
  const defaultData = {
    userId: 'test-user-123',
    sessionId: 'test-session-123',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    ...userData
  }
  
  // Mock auth function
  vi.mocked(require('@clerk/nextjs/server').auth).mockReturnValue({
    userId: defaultData.userId,
    sessionId: defaultData.sessionId,
    getToken: vi.fn().mockResolvedValue('mock-jwt-token')
  })
  
  // Mock currentUser function
  vi.mocked(require('@clerk/nextjs/server').currentUser).mockResolvedValue({
    id: defaultData.userId,
    emailAddresses: [{ emailAddress: defaultData.email }],
    firstName: defaultData.firstName,
    lastName: defaultData.lastName,
    imageUrl: 'https://example.com/avatar.jpg'
  })
  
  // Mock clerkClient
  vi.mocked(require('@clerk/nextjs/server').clerkClient.users.getUser).mockResolvedValue({
    id: defaultData.userId,
    emailAddresses: [{ emailAddress: defaultData.email }],
    firstName: defaultData.firstName,
    lastName: defaultData.lastName,
    imageUrl: 'https://example.com/avatar.jpg'
  })
  
  return defaultData
}

/**
 * Mock unauthenticated state
 */
export function mockUnauthenticated() {
  vi.mocked(require('@clerk/nextjs/server').auth).mockReturnValue({
    userId: null,
    sessionId: null,
    getToken: vi.fn().mockResolvedValue(null)
  })
  
  vi.mocked(require('@clerk/nextjs/server').currentUser).mockResolvedValue(null)
}

/**
 * Mock AI providers
 */
export function mockAIProviders() {
  // Mock AI module
  vi.doMock('ai', () => ({
    streamText: vi.fn().mockImplementation(async (options: any) => {
      return {
        textStream: async function* () {
          yield 'This is a mock AI response for testing purposes.'
        },
        text: 'This is a mock AI response for testing purposes.',
        finishReason: 'stop',
        usage: {
          promptTokens: 50,
          completionTokens: 20,
          totalTokens: 70
        }
      }
    }),
    generateText: vi.fn().mockResolvedValue({
      text: 'This is a mock AI response for testing purposes.',
      finishReason: 'stop',
      usage: {
        promptTokens: 50,
        completionTokens: 20,
        totalTokens: 70
      }
    }),
    generateId: vi.fn().mockReturnValue('mock-id-123')
  }))
}

/**
 * Mock Twitter client
 */
export function mockTwitterClient() {
  const mockTweet = {
    id: '1234567890123456789',
    text: 'This is a test tweet mentioning @testhandle',
    author: {
      id: '123456789',
      userName: 'testuser',
      name: 'Test User',
      profilePicture: 'https://example.com/avatar.jpg',
      isBlueVerified: false,
      followers: 1000
    },
    createdAt: '2024-01-15T10:30:00.000Z',
    replyCount: 5,
    retweetCount: 10,
    likeCount: 25,
    isReply: false,
    inReplyToId: null
  }
  
  // Mock twitter-client module
  vi.doMock('../../lib/twitter-client', () => ({
    twitterClient: {
      getUserMentions: vi.fn().mockResolvedValue({
        data: [mockTweet],
        meta: {
          result_count: 1,
          newest_id: mockTweet.id,
          oldest_id: mockTweet.id
        }
      }),
      getTweetFromUrl: vi.fn().mockResolvedValue(mockTweet),
      getUserInfo: vi.fn().mockResolvedValue({
        id: 'mock-user-id',
        name: 'Mock User',
        userName: 'mockuser',
        followers: 1000,
        isBlueVerified: false
      }),
      validateTwitterUrl: vi.fn().mockReturnValue(true),
      validateTwitterHandle: vi.fn().mockReturnValue(true)
    }
  }))
  
  vi.mocked(require('../../lib/twitter-client').twitterClient.getUserInfo).mockResolvedValue({
    data: mockTweet.author
  })
  
  return { mockTweet }
}

/**
 * Mock Prisma client
 */
export function mockPrismaClient() {
  const mockUser = {
    id: 'test-user-123',
    email: '<EMAIL>',
    name: 'Test User',
    planId: 'test-reply-guy',
    plan: {
      id: 'test-reply-guy',
      name: 'reply-guy',
      features: [
        { feature: 'AI_CALLS', limit: 100 },
        { feature: 'IMAGES', limit: 20 },
        { feature: 'ACCOUNTS', limit: 3 }
      ]
    },
    createdAt: new Date(),
    updatedAt: new Date()
  }
  
  const mockPrisma = {
    $connect: vi.fn(),
    $disconnect: vi.fn(),
    $transaction: vi.fn((callback) => callback(mockPrisma)),
    user: {
      findUnique: vi.fn().mockResolvedValue(mockUser),
      findMany: vi.fn().mockResolvedValue([mockUser]),
      create: vi.fn().mockResolvedValue(mockUser),
      update: vi.fn().mockResolvedValue(mockUser),
      delete: vi.fn().mockResolvedValue(mockUser),
      count: vi.fn().mockResolvedValue(1)
    },
    mention: {
      findUnique: vi.fn(),
      findMany: vi.fn().mockResolvedValue([]),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn().mockResolvedValue(0)
    },
    monitoredAccount: {
      findUnique: vi.fn(),
      findMany: vi.fn().mockResolvedValue([]),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn().mockResolvedValue(0)
    },
    aIResponse: {
      findUnique: vi.fn(),
      findMany: vi.fn().mockResolvedValue([]),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn().mockResolvedValue(0)
    },
    subscriptionPlan: {
      findUnique: vi.fn(),
      findMany: vi.fn().mockResolvedValue([]),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn().mockResolvedValue(0)
    },
    planFeature: {
      findUnique: vi.fn(),
      findMany: vi.fn().mockResolvedValue([]),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn().mockResolvedValue(0)
    },
    usageLog: {
      findUnique: vi.fn(),
      findMany: vi.fn().mockResolvedValue([]),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn().mockResolvedValue(0)
    }
  }
  
  // Mock the prisma import
  vi.doMock('../../lib/db-utils', () => ({
    prisma: mockPrisma,
    testDatabaseConnection: vi.fn().mockResolvedValue(true),
    checkRateLimit: vi.fn().mockResolvedValue({ allowed: true, remaining: 99 }),
    recordUsage: vi.fn().mockResolvedValue(undefined),
    safeDbOperation: vi.fn().mockImplementation((operation) => operation())
  }))
  
  return mockPrisma
}

/**
 * Mock environment variables
 */
export function mockEnvironmentVariables(envVars: Record<string, string> = {}) {
  const defaultEnvVars = {
    NODE_ENV: 'test',
    DATABASE_URL: 'postgresql://user:password@localhost:5432/buddychip_test',
    CLERK_SECRET_KEY: 'sk_test_mock_key',
    OPENROUTER_API_KEY: 'mock_openrouter_key',
    TWITTER_API_KEY: 'mock_twitter_key',
    OPENAI_API_KEY: 'mock_openai_key',
    ...envVars
  }
  
  Object.entries(defaultEnvVars).forEach(([key, value]) => {
    process.env[key] = value
  })
  
  return defaultEnvVars
}

/**
 * Mock console methods
 */
export function mockConsole() {
  const originalConsole = { ...console }
  
  console.log = vi.fn()
  console.info = vi.fn()
  console.warn = vi.fn()
  console.error = vi.fn()
  console.debug = vi.fn()
  
  return {
    restore: () => {
      Object.assign(console, originalConsole)
    },
    logs: {
      log: console.log as any,
      info: console.info as any,
      warn: console.warn as any,
      error: console.error as any,
      debug: console.debug as any
    }
  }
}

/**
 * Mock fetch for external API calls
 */
export function mockFetch() {
  const mockFetch = vi.fn()
  
  // Default successful response
  mockFetch.mockResolvedValue({
    ok: true,
    status: 200,
    json: vi.fn().mockResolvedValue({ success: true }),
    text: vi.fn().mockResolvedValue('success'),
    headers: new Headers()
  })
  
  global.fetch = mockFetch
  
  return mockFetch
}

/**
 * Create a mock tRPC context
 */
export function createMockTRPCContext(overrides: {
  userId?: string | null
  sessionId?: string | null
} = {}) {
  return {
    userId: overrides.userId ?? 'test-user-123',
    prisma: mockPrismaClient() as any,
    req: {
      headers: {
        'user-agent': 'test-agent',
        'x-forwarded-for': '127.0.0.1'
      }
    } as any
  } as any
}

/**
 * Mock rate limiting
 */
export function mockRateLimit(allowed: boolean = true, remaining: number = 99) {
  vi.mocked(require('../../lib/db-utils').checkRateLimit).mockResolvedValue({
    allowed,
    remaining,
    resetTime: new Date(Date.now() + 3600000) // 1 hour from now
  })
}

/**
 * Mock usage logging
 */
export function mockUsageLogging() {
  vi.mocked(require('../../lib/db-utils').recordUsage).mockResolvedValue(undefined)
}
