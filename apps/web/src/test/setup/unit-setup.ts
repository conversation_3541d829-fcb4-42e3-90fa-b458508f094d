/**
 * Unit Test Setup
 * 
 * Lightweight setup for unit tests that don't require database or external services
 */

import { beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest'
import '@testing-library/jest-dom'

// Mock external dependencies for unit tests
beforeAll(() => {
  console.log('🧪 Unit Test Setup: Starting...')
  
  // Mock Prisma client
  vi.mock('../../../prisma/generated', () => ({
    PrismaClient: vi.fn(() => ({
      $connect: vi.fn(),
      $disconnect: vi.fn(),
      $transaction: vi.fn(),
      user: {
        findUnique: vi.fn(),
        findMany: vi.fn(),
        create: vi.fn(),
        update: vi.fn(),
        delete: vi.fn(),
        count: vi.fn()
      },
      mention: {
        findUnique: vi.fn(),
        findMany: vi.fn(),
        create: vi.fn(),
        update: vi.fn(),
        delete: vi.fn(),
        count: vi.fn()
      },
      monitoredAccount: {
        findUnique: vi.fn(),
        findMany: vi.fn(),
        create: vi.fn(),
        update: vi.fn(),
        delete: vi.fn(),
        count: vi.fn()
      },
      aIResponse: {
        findUnique: vi.fn(),
        findMany: vi.fn(),
        create: vi.fn(),
        update: vi.fn(),
        delete: vi.fn(),
        count: vi.fn()
      },
      subscriptionPlan: {
        findUnique: vi.fn(),
        findMany: vi.fn(),
        create: vi.fn(),
        update: vi.fn(),
        delete: vi.fn(),
        count: vi.fn()
      },
      planFeature: {
        findUnique: vi.fn(),
        findMany: vi.fn(),
        create: vi.fn(),
        update: vi.fn(),
        delete: vi.fn(),
        count: vi.fn()
      },
      usageLog: {
        findUnique: vi.fn(),
        findMany: vi.fn(),
        create: vi.fn(),
        update: vi.fn(),
        delete: vi.fn(),
        count: vi.fn()
      }
    }))
  }))
  
  // Mock Clerk
  vi.mock('@clerk/nextjs/server', () => ({
    clerkClient: {
      users: {
        getUser: vi.fn(),
        getUserList: vi.fn(),
        createUser: vi.fn(),
        updateUser: vi.fn(),
        deleteUser: vi.fn()
      }
    },
    auth: vi.fn(() => ({
      userId: 'test-user-id',
      sessionId: 'test-session-id'
    })),
    currentUser: vi.fn(() => ({
      id: 'test-user-id',
      emailAddresses: [{ emailAddress: '<EMAIL>' }],
      firstName: 'Test',
      lastName: 'User'
    }))
  }))
  
  // Mock AI providers
  vi.mock('ai', () => ({
    streamText: vi.fn(),
    generateText: vi.fn(),
    generateId: vi.fn(() => 'test-id-123')
  }))
  
  // Mock external APIs
  vi.mock('../../lib/twitter-client', () => ({
    twitterClient: {
      getUserMentions: vi.fn(),
      getUserInfo: vi.fn()
    }
  }))
  
  // Mock environment variables
  // NODE_ENV is set via vitest config, not directly assignable
  process.env.OPENROUTER_API_KEY = 'mock_key'
  process.env.TWITTER_API_KEY = 'mock_key'
  process.env.OPENAI_API_KEY = 'mock_key'
  process.env.CLERK_SECRET_KEY = 'mock_key'
  
  console.log('✅ Unit Test Setup: Complete')
})

afterAll(() => {
  console.log('🧹 Unit Test Setup: Cleanup...')
  
  // Clear all mocks
  vi.clearAllMocks()
  vi.resetAllMocks()
  
  console.log('✅ Unit Test Cleanup: Complete')
})

beforeEach(() => {
  // Reset mocks before each test
  vi.clearAllMocks()
})

afterEach(() => {
  // Clean up after each test
  vi.clearAllMocks()
})

// Utility functions for unit tests
export const createMockUser = (overrides = {}) => ({
  id: 'test-user-id',
  email: '<EMAIL>',
  name: 'Test User',
  avatar: null,
  isAdmin: false,
  planId: 'test-reply-guy',
  personalityId: null,
  customSystemPrompt: null,
  modelId: null,
  createdAt: new Date(),
  updatedAt: new Date(),
  lastActiveAt: new Date(),
  plan: {
    id: 'test-reply-guy',
    name: 'reply-guy',
    displayName: 'Reply Guy',
    description: 'Test plan',
    price: 2000,
    billingPeriod: 'monthly',
    isActive: true,
    features: [
      { feature: 'AI_CALLS', limit: 100 },
      { feature: 'IMAGE_GENERATIONS', limit: 20 },
      { feature: 'MONITORED_ACCOUNTS', limit: 3 }
    ]
  },
  ...overrides
})

export const createMockMention = (overrides = {}) => ({
  id: 'test-mention-id',
  userId: 'test-user-id',
  accountId: 'test-account-id',
  link: 'https://x.com/test/status/123',
  content: 'This is a test mention',
  authorHandle: 'testuser',
  authorName: 'Test User',
  authorId: 'test-author-id',
  authorAvatarUrl: null,
  authorVerified: false,
  mentionedAt: new Date(),
  replyCount: 0,
  retweetCount: 0,
  likeCount: 0,
  isReply: false,
  parentTweetId: null,
  bullishScore: null,
  importanceScore: null,
  analysisData: null,
  keywords: [],
  processed: false,
  isUserTweet: false,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides
})

export const createMockAccount = (overrides = {}) => ({
  id: 'test-account-id',
  userId: 'test-user-id',
  twitterHandle: 'testhandle',
  displayName: 'Test Handle',
  twitterUserId: 'twitter-123',
  avatarUrl: null,
  isVerified: false,
  followerCount: 1000,
  isActive: true,
  lastCheckedAt: null,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides
})

export const createMockAIResponse = (overrides = {}) => ({
  id: 'test-response-id',
  userId: 'test-user-id',
  mentionId: 'test-mention-id',
  content: 'This is a test AI response',
  model: 'gemini25Flash',
  personality: 'professional',
  confidence: 0.95,
  rating: null,
  used: false,
  processingTime: BigInt(1500),
  version: '1.0',
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides
})

// Mock console methods for cleaner test output
const originalConsole = { ...console }

export const mockConsole = () => {
  console.log = vi.fn()
  console.info = vi.fn()
  console.warn = vi.fn()
  console.error = vi.fn()
}

export const restoreConsole = () => {
  console.log = originalConsole.log
  console.info = originalConsole.info
  console.warn = originalConsole.warn
  console.error = originalConsole.error
}
