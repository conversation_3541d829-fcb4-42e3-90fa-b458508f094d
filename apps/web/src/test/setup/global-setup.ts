/**
 * Global Test Setup
 * 
 * Runs once before all tests to setup the test environment
 */

import { execSync } from 'child_process'
import { PrismaClient } from '../../../prisma/generated'

export default async function globalSetup() {
  console.log('🚀 Global Test Setup: Starting...')
  
  try {
    // 1. Setup test database
    console.log('📊 Setting up test database...')
    await setupTestDatabase()
    
    // 2. Run database migrations
    console.log('🔄 Running database migrations...')
    await runMigrations()
    
    // 3. Seed test data
    console.log('🌱 Seeding test data...')
    await seedTestData()
    
    // 4. Setup external service mocks
    console.log('🎭 Setting up service mocks...')
    await setupServiceMocks()
    
    console.log('✅ Global Test Setup: Complete')
  } catch (error) {
    console.error('❌ Global Test Setup: Failed', error)
    throw error
  }
}

/**
 * Setup test database
 */
async function setupTestDatabase() {
  const testDbUrl = process.env.DATABASE_URL
  
  // In CI/test environment, allow more flexible database URLs
  const isTestEnv = process.env.NODE_ENV === 'test' || process.env.CI === 'true'
  
  if (!testDbUrl) {
    throw new Error('DATABASE_URL is required for tests')
  }
  
  // Safety check: only require "_test" in non-CI environments
  if (!isTestEnv && !testDbUrl.includes('_test')) {
    console.warn('⚠️  Warning: Using non-test database URL in test environment')
    console.warn('📝 Consider setting up a dedicated test database for safety')
  }
  
  console.log('📊 Test database URL:', testDbUrl.replace(/password=[^&\s]+/, 'password=***'))
  
  // Test database connection
  const prisma = new PrismaClient({
    datasources: {
      db: {
        url: testDbUrl
      }
    }
  })
  
  try {
    await prisma.$connect()
    console.log('✅ Test database connection successful')
  } catch (error) {
    console.error('❌ Test database connection failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

/**
 * Run database migrations
 */
async function runMigrations() {
  try {
    // Reset database to clean state
    execSync('pnpm db:migrate reset --force --skip-seed', {
      stdio: 'inherit',
      env: {
        ...process.env,
        DATABASE_URL: process.env.DATABASE_URL
      }
    })
    
    console.log('✅ Database migrations complete')
  } catch (error) {
    console.error('❌ Database migrations failed:', error)
    throw error
  }
}

/**
 * Seed test data
 */
async function seedTestData() {
  const prisma = new PrismaClient()
  
  try {
    // Create test subscription plans
    await prisma.subscriptionPlan.createMany({
      data: [
        {
          id: 'test-reply-guy',
          name: 'reply-guy',
          displayName: 'Reply Guy',
          description: 'Test plan for Reply Guy',
          price: 2000,
          isActive: true
        },
        {
          id: 'test-reply-god',
          name: 'reply-god',
          displayName: 'Reply God',
          description: 'Test plan for Reply God',
          price: 5000,
          isActive: true
        }
      ],
      skipDuplicates: true
    })
    
    // Create test plan features
    await prisma.planFeature.createMany({
      data: [
        { planId: 'test-reply-guy', feature: 'AI_CALLS', limit: 100 },
        { planId: 'test-reply-guy', feature: 'IMAGE_GENERATIONS', limit: 20 },
        { planId: 'test-reply-guy', feature: 'MONITORED_ACCOUNTS', limit: 3 },
        { planId: 'test-reply-god', feature: 'AI_CALLS', limit: 500 },
        { planId: 'test-reply-god', feature: 'IMAGE_GENERATIONS', limit: 50 },
        { planId: 'test-reply-god', feature: 'MONITORED_ACCOUNTS', limit: 10 }
      ],
      skipDuplicates: true
    })
    
    // Create test AI models
    await prisma.aIModel.createMany({
      data: [
        {
          id: 'test-gemini-flash',
          name: 'gemini25Flash',
          displayName: 'Gemini 2.5 Flash',
          description: 'Fast Gemini model',
          provider: 'google',
          modelId: 'google/gemini-2.5-flash',
          costTier: 'low',
          speed: 'fast',
          isActive: true
        },
        {
          id: 'test-gpt-4o',
          name: 'gpt4o',
          displayName: 'GPT-4o',
          description: 'Advanced GPT-4o model',
          provider: 'openai',
          modelId: 'gpt-4o',
          costTier: 'high',
          speed: 'medium',
          isActive: true
        }
      ],
      skipDuplicates: true
    })
    
    // Create test personality profiles
    await prisma.personalityProfile.createMany({
      data: [
        {
          id: 'test-professional',
          name: 'Professional',
          description: 'Professional and courteous responses',
          systemPrompt: 'You are a professional AI assistant.',
          isActive: true,
          isDefault: true
        },
        {
          id: 'test-casual',
          name: 'Casual',
          description: 'Casual and friendly responses',
          systemPrompt: 'You are a casual and friendly AI assistant.',
          isActive: true,
          isDefault: false
        }
      ],
      skipDuplicates: true
    })
    
    console.log('✅ Test data seeded successfully')
  } catch (error) {
    console.error('❌ Test data seeding failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

/**
 * Setup external service mocks
 */
async function setupServiceMocks() {
  // This will be handled by MSW in individual test files
  // Just validate that mock environment variables are set
  
  const requiredMockVars = [
    'OPENROUTER_API_KEY',
    'TWITTER_API_KEY',
    'OPENAI_API_KEY',
    'CLERK_SECRET_KEY'
  ]
  
  const missingVars = requiredMockVars.filter(key => !process.env[key])
  
  if (missingVars.length > 0) {
    console.warn('⚠️ Missing mock environment variables:', missingVars)
  }
  
  console.log('✅ Service mocks configured')
}

/**
 * Global teardown
 */
export async function globalTeardown() {
  console.log('🧹 Global Test Teardown: Starting...')
  
  try {
    // Clean up test database
    const prisma = new PrismaClient()
    
    // Delete all test data
    await prisma.$transaction([
      prisma.usageLog.deleteMany(),
      prisma.aIResponse.deleteMany(),
      prisma.mention.deleteMany(),
      prisma.monitoredAccount.deleteMany(),
      prisma.image.deleteMany(),
      prisma.user.deleteMany(),
      prisma.planFeature.deleteMany(),
      prisma.subscriptionPlan.deleteMany(),
      prisma.personalityProfile.deleteMany(),
      prisma.aIModel.deleteMany()
    ])
    
    await prisma.$disconnect()
    
    console.log('✅ Global Test Teardown: Complete')
  } catch (error) {
    console.error('❌ Global Test Teardown: Failed', error)
  }
}
