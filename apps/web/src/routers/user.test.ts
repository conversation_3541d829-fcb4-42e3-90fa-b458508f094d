/**
 * User Router Integration Tests
 * 
 * Tests for tRPC user router endpoints
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { createTRPCMsw } from 'msw-trpc'
import { appRouter } from './index'
import { createMockTRPCContext } from '../test/utils/mocks'
import { createTestUser, cleanupDatabase, testPrisma } from '../test/utils/database'

describe('User Router Integration Tests', () => {
  let testUser: any
  let trpcContext: any
  
  beforeEach(async () => {
    console.log('🧪 User Router Test: Setting up...')
    
    // Create test user
    testUser = await createTestUser({
      id: 'test-user-router-123',
      email: '<EMAIL>',
      name: 'Router Test User'
    })
    
    // Create tRPC context
    trpcContext = createMockTRPCContext({
      userId: testUser.id
    })
    
    console.log('✅ User Router Test: Setup complete')
  })
  
  afterEach(async () => {
    console.log('🧹 User Router Test: Cleaning up...')
    await cleanupDatabase()
    console.log('✅ User Router Test: Cleanup complete')
  })
  
  describe('getProfile', () => {
    it('should return user profile with plan and usage data', async () => {
      // Arrange
      const caller = appRouter.createCaller(trpcContext)
      
      // Act
      const result = await caller.user.getProfile()
      
      // Assert
      expect(result.user).toMatchObject({
        id: testUser.id,
        email: testUser.email,
        name: testUser.name
      })
      expect(result.plan).toMatchObject({
        name: 'reply-guy',
        displayName: 'Reply Guy'
      })
      
      expect(result.plan.features).toBeInstanceOf(Array)
      expect(result.plan.features.length).toBeGreaterThan(0)
      
      console.log('✅ User Router Test: Get profile works')
    })
    
    it('should throw UNAUTHORIZED when user not authenticated', async () => {
      // Arrange
      const unauthContext = createMockTRPCContext({ userId: null })
      const caller = appRouter.createCaller(unauthContext)
      
      // Act & Assert
      await expect(caller.user.getProfile()).rejects.toThrow('UNAUTHORIZED')
      
      console.log('✅ User Router Test: Unauthorized access blocked')
    })
    
    it('should create user just-in-time if not exists', async () => {
      // Arrange
      const newUserId = 'test-user-jit-456'
      const jitContext = createMockTRPCContext({ userId: newUserId })
      const caller = appRouter.createCaller(jitContext)
      
      // Verify user doesn't exist
      const existingUser = await testPrisma.user.findUnique({
        where: { id: newUserId }
      })
      expect(existingUser).toBeNull()
      
      // Act
      const result = await caller.user.getProfile()
      
      // Assert
      expect(result.user.id).toBe(newUserId)
      expect(result.plan).toBeDefined()
      
      // Verify user was created
      const createdUser = await testPrisma.user.findUnique({
        where: { id: newUserId }
      })
      expect(createdUser).not.toBeNull()
      
      console.log('✅ User Router Test: Just-in-time user creation works')
    })
  })
  
  describe('getUsage', () => {
    it('should return current usage statistics', async () => {
      // Arrange
      const caller = appRouter.createCaller(trpcContext)
      
      // Create some usage logs
      await testPrisma.usageLog.createMany({
        data: [
          {
            userId: testUser.id,
            feature: 'AI_CALLS',
            amount: 1,
            billingPeriod: '2024-01'
          },
          {
            userId: testUser.id,
            feature: 'AI_CALLS',
            amount: 1,
            billingPeriod: '2024-01'
          },
          {
            userId: testUser.id,
            feature: 'IMAGE_GENERATIONS',
            amount: 1,
            billingPeriod: '2024-01'
          }
        ]
      })
      
      // Act
      const result = await caller.user.getUsage()
      
      // Assert
      expect(result).toBeInstanceOf(Array)
      expect(result.length).toBeGreaterThan(0)
      
      const aiCallsFeature = result.find(f => f.feature === 'AI_CALLS')
      const imageFeature = result.find(f => f.feature === 'IMAGE_GENERATIONS')
      const accountsFeature = result.find(f => f.feature === 'MONITORED_ACCOUNTS')
      
      expect(aiCallsFeature).toMatchObject({
        feature: 'AI_CALLS',
        allowed: expect.any(Boolean),
        currentUsage: expect.any(Number),
        limit: expect.any(Number)
      })
      
      expect(imageFeature).toMatchObject({
        feature: 'IMAGE_GENERATIONS',
        allowed: expect.any(Boolean),
        currentUsage: expect.any(Number),
        limit: expect.any(Number)
      })
      
      expect(accountsFeature).toMatchObject({
        feature: 'MONITORED_ACCOUNTS',
        allowed: expect.any(Boolean),
        currentUsage: expect.any(Number),
        limit: expect.any(Number)
      })
      
      console.log('✅ User Router Test: Usage statistics work')
    })
    
    it('should handle user with no usage logs', async () => {
      // Arrange
      const caller = appRouter.createCaller(trpcContext)
      
      // Act
      const result = await caller.user.getUsage()
      
      // Assert
      expect(result).toBeInstanceOf(Array)
      const aiCallsFeature = result.find(f => f.feature === 'AI_CALLS')
      const imageFeature = result.find(f => f.feature === 'IMAGE_GENERATIONS')
      const accountsFeature = result.find(f => f.feature === 'MONITORED_ACCOUNTS')
      
      expect(aiCallsFeature?.currentUsage).toBe(0)
      expect(imageFeature?.currentUsage).toBe(0)
      expect(accountsFeature?.currentUsage).toBe(0)
      
      console.log('✅ User Router Test: Empty usage handling works')
    })
  })
  
  describe('updateProfile', () => {
    it('should update user profile data', async () => {
      // Arrange
      const caller = appRouter.createCaller(trpcContext)
      const updateData = {
        name: 'Updated Test User'
      }
      
      // Act
      const result = await caller.user.updateProfile(updateData)
      
      // Assert
      expect(result.success).toBe(true)
      expect(result.user.name).toBe(updateData.name)
      
      // Verify in database
      const updatedUser = await testPrisma.user.findUnique({
        where: { id: testUser.id }
      })
      expect(updatedUser?.name).toBe(updateData.name)
      
      console.log('✅ User Router Test: Profile update works')
    })
    
    it('should validate input data', async () => {
      // Arrange
      const caller = appRouter.createCaller(trpcContext)
      const invalidData = {
        name: '', // Empty name should be invalid
        customSystemPrompt: 'a'.repeat(10001) // Too long
      }
      
      // Act & Assert
      await expect(caller.user.updateProfile(invalidData)).rejects.toThrow()
      
      console.log('✅ User Router Test: Input validation works')
    })
  })
  
  describe('updatePersonality', () => {
    it('should update user personality selection', async () => {
      // Arrange
      const caller = appRouter.createCaller(trpcContext)
      
      // Create test personality
      const personality = await testPrisma.personalityProfile.create({
        data: {
          id: 'test-personality-casual',
          name: 'Casual',
          description: 'Casual and friendly',
          systemPrompt: 'Be casual and friendly',
          isActive: true,
          isDefault: false
        }
      })
      
      // Act
      const result = await caller.user.updatePersonality({
        personalityId: personality.id
      })
      
      // Assert
      expect(result.success).toBe(true)
      
      // Verify in database
      const updatedUser = await testPrisma.user.findUnique({
        where: { id: testUser.id }
      })
      expect(updatedUser?.personalityId).toBe(personality.id)
      
      console.log('✅ User Router Test: Personality update works')
    })
    
    it('should handle null personality ID', async () => {
      // Arrange
      const caller = appRouter.createCaller(trpcContext)
      
      // Act
      const result = await caller.user.updatePersonality({
        personalityId: null
      })
      
      // Assert
      expect(result.success).toBe(true)
      
      console.log('✅ User Router Test: Null personality handling works')
    })
  })
  
  describe('updateSelectedModel', () => {
    it('should update user AI model selection', async () => {
      // Arrange
      const caller = appRouter.createCaller(trpcContext)
      
      // Create test AI model
      const model = await testPrisma.aIModel.create({
        data: {
          id: 'test-model-gpt4',
          name: 'gpt4o',
          displayName: 'GPT-4o',
          description: 'Advanced GPT-4o model',
          provider: 'openai',
          modelId: 'gpt-4o',
          costTier: 'high',
          speed: 'medium',
          isActive: true
        }
      })
      
      // Act
      const result = await caller.user.updateSelectedModel({
        modelId: model.id
      })
      
      // Assert
      expect(result.success).toBe(true)
      
      // Verify in database
      const updatedUser = await testPrisma.user.findUnique({
        where: { id: testUser.id }
      })
      expect(updatedUser?.modelId).toBe(model.id)
      
      console.log('✅ User Router Test: Model update works')
    })
    
    it('should reject inactive AI model', async () => {
      // Arrange
      const caller = appRouter.createCaller(trpcContext)
      
      // Create inactive AI model
      const inactiveModel = await testPrisma.aIModel.create({
        data: {
          id: 'test-model-inactive',
          name: 'inactive-model',
          displayName: 'Inactive Model',
          description: 'Inactive test model',
          provider: 'test',
          modelId: 'inactive-model',
          costTier: 'low',
          speed: 'fast',
          isActive: false
        }
      })
      
      // Act & Assert
      await expect(caller.user.updateSelectedModel({
        modelId: inactiveModel.id
      })).rejects.toThrow('AI model not found')
      
      console.log('✅ User Router Test: Inactive model rejection works')
    })
  })
  
  describe('canUseFeature', () => {
    it('should return feature access status', async () => {
      // Arrange
      const caller = appRouter.createCaller(trpcContext)
      
      // Act
      const result = await caller.user.canUseFeature({
        feature: 'AI_CALLS'
      })
      
      // Assert
      expect(result).toMatchObject({
        allowed: expect.any(Boolean),
        currentUsage: expect.any(Number),
        limit: expect.any(Number)
      })
      
      console.log('✅ User Router Test: Feature access check works')
    })
    
    it('should handle invalid feature type', async () => {
      // Arrange
      const caller = appRouter.createCaller(trpcContext)
      
      // Act & Assert
      await expect(caller.user.canUseFeature({
        feature: 'INVALID_FEATURE' as any
      })).rejects.toThrow()
      
      console.log('✅ User Router Test: Invalid feature handling works')
    })
  })
})
